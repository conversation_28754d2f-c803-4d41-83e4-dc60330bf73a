// 图片上传工具类
const app = getApp();

/**
 * 上传单张图片到云存储
 * @param {string} filePath 本地文件路径
 * @param {string} folder 云存储文件夹名称
 * @returns {Promise} 返回上传结果
 */
function uploadImage(filePath, folder = 'images') {
  return new Promise((resolve, reject) => {
    // 生成唯一文件名
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const extension = filePath.split('.').pop() || 'jpg';
    const cloudPath = `${folder}/${timestamp}-${random}.${extension}`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: (res) => {
        resolve({
          success: true,
          fileID: res.fileID,
          cloudPath: cloudPath
        });
      },
      fail: (err) => {
        console.error('上传图片失败:', err);
        reject({
          success: false,
          error: err
        });
      }
    });
  });
}

/**
 * 上传多张图片到云存储
 * @param {Array} filePaths 本地文件路径数组
 * @param {string} folder 云存储文件夹名称
 * @returns {Promise} 返回上传结果数组
 */
function uploadImages(filePaths, folder = 'images') {
  const uploadPromises = filePaths.map(filePath => uploadImage(filePath, folder));
  return Promise.all(uploadPromises);
}

/**
 * 选择并上传单张图片
 * @param {Object} options 选择图片的配置选项
 * @param {string} folder 云存储文件夹名称
 * @returns {Promise} 返回上传结果
 */
function chooseAndUploadImage(options = {}, folder = 'images') {
  const defaultOptions = {
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      ...finalOptions,
      success: async (res) => {
        try {
          app.showLoading('上传中...');
          const uploadResult = await uploadImage(res.tempFilePaths[0], folder);
          app.hideLoading();
          
          if (uploadResult.success) {
            app.showSuccess('上传成功');
            resolve(uploadResult);
          } else {
            app.showError('上传失败');
            reject(uploadResult);
          }
        } catch (error) {
          app.hideLoading();
          app.showError('上传失败');
          reject(error);
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 选择并上传多张图片
 * @param {Object} options 选择图片的配置选项
 * @param {string} folder 云存储文件夹名称
 * @returns {Promise} 返回上传结果数组
 */
function chooseAndUploadImages(options = {}, folder = 'images') {
  const defaultOptions = {
    count: 9,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      ...finalOptions,
      success: async (res) => {
        try {
          app.showLoading('上传中...');
          const uploadResults = await uploadImages(res.tempFilePaths, folder);
          app.hideLoading();
          
          const successResults = uploadResults.filter(result => result.success);
          const failedResults = uploadResults.filter(result => !result.success);
          
          if (successResults.length > 0) {
            app.showSuccess(`成功上传${successResults.length}张图片`);
            resolve({
              success: true,
              results: successResults,
              failed: failedResults
            });
          } else {
            app.showError('上传失败');
            reject({
              success: false,
              results: [],
              failed: failedResults
            });
          }
        } catch (error) {
          app.hideLoading();
          app.showError('上传失败');
          reject(error);
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 删除云存储中的文件
 * @param {string|Array} fileIDs 文件ID或文件ID数组
 * @returns {Promise} 返回删除结果
 */
function deleteCloudFiles(fileIDs) {
  const fileList = Array.isArray(fileIDs) ? fileIDs : [fileIDs];
  
  return new Promise((resolve, reject) => {
    wx.cloud.deleteFile({
      fileList: fileList,
      success: (res) => {
        resolve({
          success: true,
          fileList: res.fileList
        });
      },
      fail: (err) => {
        console.error('删除文件失败:', err);
        reject({
          success: false,
          error: err
        });
      }
    });
  });
}

/**
 * 获取云存储文件的临时链接
 * @param {string|Array} fileIDs 文件ID或文件ID数组
 * @returns {Promise} 返回临时链接
 */
function getTempFileURL(fileIDs) {
  const fileList = Array.isArray(fileIDs) ? fileIDs : [fileIDs];
  
  return new Promise((resolve, reject) => {
    wx.cloud.getTempFileURL({
      fileList: fileList,
      success: (res) => {
        resolve({
          success: true,
          fileList: res.fileList
        });
      },
      fail: (err) => {
        console.error('获取临时链接失败:', err);
        reject({
          success: false,
          error: err
        });
      }
    });
  });
}

/**
 * 压缩图片
 * @param {string} src 图片路径
 * @param {number} quality 压缩质量 0-100
 * @returns {Promise} 返回压缩后的图片路径
 */
function compressImage(src, quality = 80) {
  return new Promise((resolve, reject) => {
    wx.compressImage({
      src: src,
      quality: quality,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (err) => {
        console.error('压缩图片失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 预览图片
 * @param {string} current 当前显示图片的链接
 * @param {Array} urls 需要预览的图片链接列表
 */
function previewImage(current, urls = []) {
  wx.previewImage({
    current: current,
    urls: urls.length > 0 ? urls : [current]
  });
}

module.exports = {
  uploadImage,
  uploadImages,
  chooseAndUploadImage,
  chooseAndUploadImages,
  deleteCloudFiles,
  getTempFileURL,
  compressImage,
  previewImage
};
