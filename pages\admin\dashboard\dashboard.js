// pages/admin/dashboard/dashboard.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    adminInfo: {},
    currentTime: '',
    stats: {
      totalUsers: 0,
      totalReservations: 0,
      activeReservations: 0,
      totalActivities: 0
    },
    recentReservations: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkAdminAuth()
    this.updateTime()
    this.loadDashboardData()

    // 每分钟更新时间
    this.timeInterval = setInterval(() => {
      this.updateTime()
    }, 60000)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadDashboardData()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },

  /**
   * 检查管理员权限
   */
  checkAdminAuth() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限，无法访问此页面',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/user/home/<USER>'
          })
        }
      })
      return false
    }

    this.setData({ adminInfo: userInfo })
    return true
  },

  /**
   * 更新当前时间
   */
  updateTime() {
    const now = new Date()
    const timeStr = now.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    this.setData({ currentTime: timeStr })
  },

  /**
   * 加载仪表板数据
   */
  async loadDashboardData() {
    try {
      this.setData({ isLoading: true })

      // 并行加载统计数据和最近预约
      const [statsRes, recentRes] = await Promise.all([
        this.loadStats(),
        this.loadRecentReservations()
      ])

      this.setData({
        stats: statsRes,
        recentReservations: recentRes,
        isLoading: false
      })
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          action: 'getAdminStats'
        }
      })

      console.log('统计数据云函数返回:', res)

      if (res.result && (res.result.success || res.result.code === 0)) {
        return res.result.data
      } else {
        throw new Error(res.result?.message || '获取统计数据失败')
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      return {
        totalUsers: 0,
        totalReservations: 0,
        activeReservations: 0,
        totalActivities: 0
      }
    }
  },

  /**
   * 加载最近预约
   */
  async loadRecentReservations() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'getRecentReservations',
          limit: 5
        }
      })

      console.log('最近预约云函数返回:', res)

      if (res.result && (res.result.success || res.result.code === 0)) {
        return res.result.data.map(item => ({
          ...item,
          reserveDate: item.reserveDate
        }))
      } else {
        throw new Error(res.result?.message || '获取最近预约失败')
      }
    } catch (error) {
      console.error('加载最近预约失败:', error)
      return []
    }
  },

  /**
   * 跳转到预约管理
   */
  goToReservations() {
    wx.navigateTo({
      url: '/pages/admin/reservations/reservations'
    })
  },

  /**
   * 跳转到内容管理
   */
  goToContent() {
    wx.navigateTo({
      url: '/pages/admin/content/content'
    })
  },

  /**
   * 新增活动
   */
  addActivity() {
    wx.showToast({
      title: '新增活动功能开发中',
      icon: 'none'
    })
  },

  /**
   * 查看数据报表
   */
  viewReports() {
    wx.showToast({
      title: '数据报表功能开发中',
      icon: 'none'
    })
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return ''

    const date = new Date(dateStr)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const diffDays = Math.floor((targetDate - today) / (24 * 60 * 60 * 1000))

    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '明天'
    } else if (diffDays === -1) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadDashboardData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 处理返回按钮点击事件
   */
  onNavigateBack() {
    wx.showModal({
      title: '退出登录',
      content: '是否退出登录？',
      confirmText: '退出',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.logout()
        }
      }
    })
    return false // 阻止默认返回行为
  },

  /**
   * 退出登录
   */
  logout() {
    try {
      // 清除登录信息
      wx.removeStorageSync('userInfo')

      // 清除全局数据
      const app = getApp()
      app.globalData.userInfo = null

      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })

      // 延迟跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/user/home/<USER>'
        })
      }, 1500)
    } catch (error) {
      console.error('退出登录失败:', error)
      wx.showToast({
        title: '退出失败',
        icon: 'none'
      })
    }
  }
})