/* pages/user/reserved/reserved.wxss */
.reserved-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 统计信息区域 */
.stats-section {
  background: var(--surface-color);
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.stats-number.active {
  color: var(--primary-color);
}

.stats-number.completed {
  color: #4CAF50;
}

.stats-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 筛选标签区域 */
.filter-section {
  background: var(--surface-color);
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  background: var(--background-color);
  text-align: center;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: var(--primary-color);
  color: #2196F3;
}

/* 预约列表 */
.reservations-list {
  padding: 0 20rpx;
}

.reservation-item {
  background: var(--surface-color);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
}

.status-indicator.active {
  background: var(--primary-color);
}

.status-indicator.cancelled {
  background: #ff4444;
}

/* 预约内容 */
.reservation-content {
  padding: 30rpx 30rpx 30rpx 38rpx;
}

/* 服务信息 */
.service-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.service-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 20rpx;
}

.service-type {
  flex-shrink: 0;
}

.type-tag {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: white;
}

.type-tag.visit {
  background: #4CAF50;
}

.type-tag.activity {
  background: #FF9800;
}

/* 预约信息 */
.booking-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  width: 32rpx;
  text-align: center;
}

.info-text {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 预约底部 */
.reservation-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid var(--divider-color);
}

.status-text {
  font-size: 26rpx;
  font-weight: 500;
}

.status-text.active {
  color: var(--primary-color);
}

.status-text.cancelled {
  color: #ff4444;
}

.actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: var(--background-color);
  color: var(--text-primary);
  border: 1rpx solid var(--divider-color);
}

.action-btn.danger {
  background: #ffebee;
  color: #ff4444;
  border: 1rpx solid #ffcdd2;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
}

/* 用户备注样式 */
.user-note-row {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
  position: relative;
}

.note-content {
  flex: 1;
  margin-right: 40rpx;
}

.note-content .placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

.note-edit-btn {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
}

.edit-icon {
  font-size: 24rpx;
  color: white;
}

.note-edit-btn:active {
  transform: translateY(-50%) scale(0.9);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}