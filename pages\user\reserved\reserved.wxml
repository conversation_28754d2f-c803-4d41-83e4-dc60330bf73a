<!--pages/user/reserved/reserved.wxml-->
<view class="reserved-container">
  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-item">
      <view class="stats-number">{{stats.total}}</view>
      <view class="stats-label">总预约</view>
    </view>
    <view class="stats-item">
      <view class="stats-number active">{{stats.active}}</view>
      <view class="stats-label">进行中</view>
    </view>
    <view class="stats-item">
      <view class="stats-number completed">{{stats.completed}}</view>
      <view class="stats-label">已完成</view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-section">
    <view
      class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
      bindtap="switchFilter"
      data-filter="all"
    >
      全部
    </view>
    <view
      class="filter-tab {{currentFilter === 'active' ? 'active' : ''}}"
      bindtap="switchFilter"
      data-filter="active"
    >
      进行中
    </view>
    <view
      class="filter-tab {{currentFilter === 'cancelled' ? 'active' : ''}}"
      bindtap="switchFilter"
      data-filter="cancelled"
    >
      已取消
    </view>
  </view>

  <!-- 预约列表 -->
  <view class="reservations-list">
    <view
      class="reservation-item"
      wx:for="{{filteredReservations}}"
      wx:key="id"
      data-id="{{item._id}}"
    >
      <!-- 预约状态标识 -->
      <view class="status-indicator {{item.status}}"></view>

      <!-- 预约内容 -->
      <view class="reservation-content">
        <!-- 服务信息 -->
        <view class="service-info">
          <view class="service-title">{{item.activityTitle || '康养服务'}}</view>
        </view>

        <!-- 预约信息 -->
        <view class="booking-info">
          <view class="info-row">
            <text class="info-icon">👤</text>
            <text class="info-text">{{item.userName}}</text>
          </view>
          <view class="info-row">
            <text class="info-icon">📱</text>
            <text class="info-text">{{item.userPhone}}</text>
          </view>
          <view class="info-row">
            <text class="info-icon">📅</text>
            <text class="info-text">{{item.reserveDate}}</text>
          </view>
          <view class="info-row" wx:if="{{item.remark}}">
            <text class="info-icon">📝</text>
            <text class="info-text">{{item.remark}}</text>
          </view>
          <view class="info-row" wx:if="{{item.status === 'cancelled' && item.cancelReason}}">
            <text class="info-icon">❌</text>
            <text class="info-text">取消原因：{{item.cancelReason}}</text>
          </view>

          <!-- 用户备注 -->
          <view class="info-row user-note-row">
            <text class="info-icon">💭</text>
            <view class="note-content">
              <text class="info-text" wx:if="{{item.userNote}}">我的备注：{{item.userNote}}</text>
              <text class="info-text placeholder" wx:else>点击添加备注</text>
            </view>
            <view class="note-edit-btn" bindtap="editUserNote" data-id="{{item._id}}" data-note="{{item.userNote}}">
              <text class="edit-icon">✏️</text>
            </view>
          </view>
        </view>

        <!-- 状态和操作 -->
        <view class="reservation-footer">
          <view class="status-text {{item.status}}">
            {{item.status === 'active' ? '预约中' : '已取消'}}
          </view>
          <view class="actions">
            <view
              class="action-btn danger"
              wx:if="{{item.status === 'active'}}"
              bindtap="cancelReservation"
              catchtap
              data-id="{{item._id}}"
            >
              取消
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && filteredReservations.length === 0}}">
    <view class="empty-icon">📅</view>
    <view class="empty-title">暂无预约记录</view>
    <view class="empty-desc">
      {{currentFilter === 'all' ? '您还没有任何预约记录' :
        currentFilter === 'active' ? '您当前没有进行中的预约' : '您没有已取消的预约'}}
    </view>
    <view class="empty-action" bindtap="goToHome">
      <text>去预约</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>