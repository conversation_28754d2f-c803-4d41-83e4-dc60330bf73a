﻿<!--pages/user/booking/booking.wxml-->
<view class="booking-container">
  <!-- 活动信息卡片 -->
  <view class="activity-card" wx:if="{{activity}}">
    <view class="activity-image" wx:if="{{activity.imageUrl}}">
      <image src="{{activity.imageUrl}}" mode="aspectFill" />
    </view>
    <view class="activity-info">
      <view class="activity-title">{{activity.title}}</view>
      <view class="activity-desc">{{activity.description}}</view>
      <view class="activity-meta">
        <view class="meta-item">
          <text class="meta-icon"></text>
          <text class="meta-text">{{activity.location || '待定'}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-icon"></text>
          <text class="meta-text">{{activity.type === 'visit' ? '参观服务' : '活动服务'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 预约表单 -->
  <view class="form-section">
    <view class="section-title">
      <text class="title-icon"></text>
      <text>预约信息</text>
    </view>

    <!-- 姓名 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>姓名</text>
      </view>
      <input style="width: 654rpx; display: block; box-sizing: border-box"
        class="form-input"
        placeholder="请输入您的姓名"
        value="{{formData.userName}}"
        bindinput="onInputChange"
        data-field="userName"
        maxlength="20"
      />
    </view>

    <!-- 手机号 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>手机号</text>
      </view>
      <input style="width: 654rpx; display: block; box-sizing: border-box"
        class="form-input"
        placeholder="请输入您的手机号"
        value="{{formData.userPhone}}"
        bindinput="onInputChange"
        data-field="userPhone"
        type="number"
        maxlength="11"
      />
    </view>

    <!-- 预约日期 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>预约日期</text>
      </view>
      <picker
        mode="date"
        value="{{formData.reserveDate}}"
        start="{{minDate}}"
        end="{{maxDate}}"
        bindchange="onDateChange"
        class="date-picker"
      >
        <view class="picker-display">
          <text class="picker-text">{{formData.reserveDate || '请选择日期'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 备注 -->
    <view class="form-item">
      <view class="form-label">
        <text>备注</text>
      </view>
      <textarea style="width: 654rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx"
        class="form-textarea"
        placeholder="请输入备注信息（选填）"
        value="{{formData.remark}}"
        bindinput="onInputChange"
        data-field="remark"
        maxlength="200"
        show-confirm-bar="{{false}}"
      />
    </view>
  </view>

  <!-- 预约须知 -->
  <view class="notice-section">
    <view class="section-title">
      <text class="title-icon"></text>
      <text>预约须知</text>
    </view>
    <view class="notice-content">
      <view class="notice-item"> 请提前1天进行预约，当天预约可能无法安排</view>
      <view class="notice-item"> 预约成功后，我们会在24小时内与您联系确认</view>
      <view class="notice-item"> 如需取消或修改预约，请提前4小时联系我们</view>
      <view class="notice-item"> 参观时请携带有效身份证件</view>
      <view class="notice-item"> 服务时间：周一至周日 9:00-17:00</view>
    </view>
  </view>
</view>

<!-- 底部提交按钮 -->
<view class="bottom-submit">
  <button
    class="submit-btn"
    bindtap="submitBooking"
    loading="{{isSubmitting}}"
    disabled="{{isSubmitting}}"
  >
    {{isSubmitting ? '提交中...' : '确认预约'}}
  </button>
</view>

<!-- 加载状态 -->
<!-- <view class="loading-overlay" wx:if="{{isLoading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> -->

<!-- 空状态 -->
<view class="empty-state" wx:if="{{!isLoading && !activity}}">
  <view class="empty-icon"></view>
  <view class="empty-text">活动不存在或已结束</view>
  <button class="empty-btn" bindtap="navigateBack">返回</button>
</view>
