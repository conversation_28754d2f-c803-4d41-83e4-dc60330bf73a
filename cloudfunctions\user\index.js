// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  
  try {
    switch (action) {
      case 'getUserStats':
        return await getUserStats(event)
      case 'updateProfile':
        return await updateProfile(event)
      case 'getAdminStats':
        return await getAdminStats(event)
      default:
        return {
          code: -1,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('用户操作失败:', error)
    return {
      code: -1,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 获取用户统计数据
async function getUserStats(event) {
  const { userId } = event
  
  // 获取用户信息
  const userResult = await db.collection('users').doc(userId).get()
  if (!userResult.data) {
    return {
      code: -1,
      message: '用户不存在'
    }
  }
  
  const user = userResult.data
  
  // 获取预约统计
  const reservationStats = await db.collection('reservations')
    .where({
      userId: userId
    })
    .get()
  
  const totalReservations = reservationStats.data.length
  const activeReservations = reservationStats.data.filter(item => item.status === 'active').length
  const completedReservations = reservationStats.data.filter(item => item.status === 'cancelled').length

  // 计算加入天数
  const createTime = new Date(user.createTime)
  const now = new Date()
  const joinDays = Math.floor((now - createTime) / (1000 * 60 * 60 * 24))

  return {
    code: 0,
    message: '获取成功',
    data: {
      totalReservations,
      activeReservations,
      completedReservations,
      joinDays: joinDays > 0 ? joinDays : 1
    }
  }
}

// 获取管理员统计数据
async function getAdminStats(event) {
  try {
    // 获取总用户数
    const usersResult = await db.collection('users').count()
    const totalUsers = usersResult.total

    // 获取总预约数
    const reservationsResult = await db.collection('reservations').count()
    const totalReservations = reservationsResult.total

    // 获取活跃预约数（状态为active或confirmed）
    const activeReservationsResult = await db.collection('reservations')
      .where({
        status: db.command.in(['active', 'confirmed'])
      })
      .count()
    const activeReservations = activeReservationsResult.total

    // 获取总活动数
    const activitiesResult = await db.collection('activities').count()
    const totalActivities = activitiesResult.total

    return {
      success: true,
      code: 0,
      message: '获取成功',
      data: {
        totalUsers,
        totalReservations,
        activeReservations,
        totalActivities
      }
    }
  } catch (error) {
    console.error('获取管理员统计数据失败:', error)
    return {
      success: false,
      code: -1,
      message: '获取统计数据失败',
      error: error.message
    }
  }
}

// 更新用户资料
async function updateProfile(event) {
  const { profile } = event
  const { nickName, address, avatarUrl } = profile

  // 验证数据
  if (!nickName || !nickName.trim()) {
    return {
      success: false,
      code: -1,
      message: '昵称不能为空'
    }
  }

  try {
    // 获取当前用户信息
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    // 查找用户记录
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        code: -1,
        message: '用户不存在'
      }
    }

    const userId = userResult.data[0]._id

    // 更新用户信息
    const updateData = {
      nickName: nickName.trim(),
      updateTime: new Date()
    }

    // 添加可选字段
    if (address !== undefined && address !== null) {
      updateData.address = address.trim()
    }

    if (avatarUrl !== undefined && avatarUrl !== null) {
      updateData.avatarUrl = avatarUrl
    }

    await db.collection('users').doc(userId).update({
      data: updateData
    })

    // 返回更新后的用户信息
    const updatedUser = await db.collection('users').doc(userId).get()

    return {
      success: true,
      code: 0,
      message: '更新成功',
      data: updatedUser.data
    }
  } catch (error) {
    console.error('更新用户资料失败:', error)
    return {
      success: false,
      code: -1,
      message: '更新失败，请重试',
      error: error.message
    }
  }
}
