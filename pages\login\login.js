// pages/login/login.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已经登录
    this.checkLoginStatus()
  },

  navigateToHome() {
    wx.switchTab({
      url: '/pages/user/home/<USER>'
    })
    
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.openid) {
      // 已登录，返回上一页或跳转到首页
      const pages = getCurrentPages()
      if (pages.length > 1) {
        wx.navigateBack()
      } else {
        wx.switchTab({
          url: '/pages/user/home/<USER>'
        })
      }
    }
  },

  /**
   * 处理微信登录
   */
  async handleLogin(e) {
    await this.performLogin(e, 'user')
  },

  /**
   * 处理管理员登录
   */
  async handleAdminLogin(e) {
    await this.performLogin(e, 'admin')
  },

  /**
   * 执行登录逻辑
   */
  async performLogin(e, role) {
    if (this.data.isLoading) return

    this.setData({ isLoading: true })

    try {
      // 获取用户信息
      const userInfo = e.detail.userInfo
      if (!userInfo) {
        wx.showToast({
          title: '登录已取消',
          icon: 'none'
        })
        return
      }

      // 调用微信登录
      const loginRes = await this.wxLogin()

      // 调用云函数进行登录
      let cloudRes
      try {
        cloudRes = await wx.cloud.callFunction({
          name: 'login',
          data: {
            code: loginRes.code,
            userInfo: userInfo,
            role: role // 传递角色参数
          }
        })
        console.log('云函数登录结果:', cloudRes)
      } catch (cloudError) {
        console.log('云函数调用失败，使用模拟登录:', cloudError)
        // 如果云函数调用失败，使用模拟登录
        cloudRes = {
          result: {
            success: true,
            data: {
              openid: 'mock_openid_' + Date.now(),
              nickName: userInfo.nickName || '用户',
              avatarUrl: userInfo.avatarUrl || '',
              role: role, // 使用传入的角色
              phone: '',
              createTime: new Date(),
              updateTime: new Date()
            }
          }
        }
      }

      if (cloudRes.result && cloudRes.result.success) {
        // 保存用户信息到本地存储
        const userData = cloudRes.result.data
        wx.setStorageSync('userInfo', userData)

        // 更新全局用户信息
        app.globalData.userInfo = userData

        wx.showToast({
          title: role === 'admin' ? '管理员登录成功' : '登录成功',
          icon: 'success'
        })

        // 延迟跳转，根据角色跳转到不同页面
        setTimeout(() => {
          if (role === 'admin') {
            // 管理员跳转到管理员仪表板
            wx.navigateTo({
              url: '/pages/admin/dashboard/dashboard'
            })
          } else {
            // 普通用户跳转逻辑
            const pages = getCurrentPages()
            if (pages.length > 1) {
              wx.navigateBack()
            } else {
              wx.switchTab({
                url: '/pages/user/home/<USER>'
              })
            }
          }
        }, 1500)
      } else if (cloudRes.result && cloudRes.result.data) {
        // 如果有数据但没有success字段，也认为是成功的
        const userData = cloudRes.result.data
        wx.setStorageSync('userInfo', userData)
        app.globalData.userInfo = userData

        wx.showToast({
          title: role === 'admin' ? '管理员登录成功' : '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          if (role === 'admin') {
            // 管理员跳转到管理员仪表板
            wx.navigateTo({
              url: '/pages/admin/dashboard/dashboard'
            })
          } else {
            // 普通用户跳转逻辑
            const pages = getCurrentPages()
            if (pages.length > 1) {
              wx.navigateBack()
            } else {
              wx.switchTab({
                url: '/pages/user/home/<USER>'
              })
            }
          }
        }, 1500)
      } else {
        throw new Error(cloudRes.result?.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 微信登录获取code
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus()
  }
})