/* pages/user/booking/booking.wxss */
.booking-container {
  padding: 20rpx;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 活动信息卡片 */
.activity-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.activity-image {
  width: 100%;
  height: 300rpx;
  position: relative;
}

.activity-image image {
  width: 100%;
  height: 100%;
}

.activity-info {
  padding: 30rpx;
}

.activity-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.activity-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #888;
}

.meta-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 表单区域 */
.form-section, .notice-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-right: 8rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #2196F3;
  background: white;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  border-color: #2196F3;
  background: white;
}

/* 日期选择器 */
.date-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.picker-display:active {
  background: #f0f0f0;
}

.picker-text {
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
  transform: rotate(90deg);
}

/* 时间网格 */
.time-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.time-item {
  flex: 0 0 calc(25% - 12rpx);
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  background: #fafafa;
  transition: all 0.3s ease;
}

.time-item:active {
  transform: scale(0.95);
}

.time-item.selected {
  background: #2196F3;
  border-color: #2196F3;
  color: white;
  font-weight: bold;
}

/* 预约须知 */
.notice-content {
  line-height: 1.8;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  padding-left: 20rpx;
  position: relative;
}

.notice-item:last-child {
  margin-bottom: 0;
}

/* 底部提交按钮 */
.bottom-submit {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: white;
  border-top: 2rpx solid #f0f0f0;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
  transform: none;
  opacity: 1;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
  height: 72rpx;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 36rpx;
  font-size: 28rpx;
}
