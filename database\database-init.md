# 智慧养老小程序数据库初始化文档

## 数据库集合设计

本文档详细说明了智慧养老小程序的数据库结构、权限配置和初始化步骤。

### 1. 用户表 (users)

**集合名称**: `users`

**字段结构**:
```javascript
{
  _id: "用户ID",
  openid: "微信openid",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  role: "user|admin", // 用户角色
  phone: "手机号",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

**权限设置**:
- 读权限：仅创建者可读
- 写权限：仅创建者可写

**索引**:
- openid (唯一索引)
- role
- createTime

### 2. 活动表 (activities)

**集合名称**: `activities`

**字段结构**:
```javascript
{
  _id: "活动ID",
  title: "活动标题",
  description: "活动描述",
  type: "visit|activity", // visit:参观, activity:活动
  imageUrl: "主图URL",
  houseIntro: "房屋介绍",
  environmentIntro: "环境介绍", 
  serviceIntro: "服务介绍",
  location: "位置信息",
  phone: "联系电话",
  status: "draft|published|ended", // 状态
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

**权限设置**:
- 读权限：所有用户可读
- 写权限：仅管理员可写

**索引**:
- type
- status
- createTime
- title (文本索引)

### 3. 文章表 (articles)

**集合名称**: `articles`

**字段结构**:
```javascript
{
  _id: "文章ID",
  title: "文章标题",
  content: "文章内容",
  imageUrl: "主图URL",
  summary: "文章摘要",
  status: "draft|published",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

**权限设置**:
- 读权限：所有用户可读
- 写权限：仅管理员可写

**索引**:
- status
- createTime
- title (文本索引)

### 4. 预约表 (reservations)

**集合名称**: `reservations`

**字段结构**:
```javascript
{
  _id: "预约ID",
  userId: "用户ID",
  activityId: "活动ID",
  userName: "预约人姓名",
  userPhone: "预约人电话",
  reserveTime: "预约时间",
  remark: "备注信息",
  status: "active|cancelled", // 预约状态
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

**权限设置**:
- 读权限：仅创建者和管理员可读
- 写权限：仅创建者和管理员可写

**索引**:
- userId
- activityId
- status
- createTime
- userName (文本索引)
- userPhone (文本索引)

### 5. 轮播图表 (banners)

**集合名称**: `banners`

**字段结构**:
```javascript
{
  _id: "轮播图ID",
  imageUrl: "图片URL",
  title: "标题",
  linkUrl: "跳转链接",
  sort: "排序",
  status: "active|inactive",
  createTime: "创建时间"
}
```

**权限设置**:
- 读权限：所有用户可读
- 写权限：仅管理员可写

**索引**:
- status
- sort
- createTime

## 数据库权限配置

### 权限规则设置

在微信云开发控制台中，需要为每个集合设置相应的权限规则：

#### 1. users 集合权限
```javascript
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

#### 2. activities 集合权限
```javascript
{
  "read": true,
  "write": "get('database.users.${auth.openid}').role == 'admin'"
}
```

#### 3. articles 集合权限
```javascript
{
  "read": true,
  "write": "get('database.users.${auth.openid}').role == 'admin'"
}
```

#### 4. reservations 集合权限
```javascript
{
  "read": "doc.userId == auth.openid || get('database.users.${auth.openid}').role == 'admin'",
  "write": "doc.userId == auth.openid || get('database.users.${auth.openid}').role == 'admin'"
}
```

#### 5. banners 集合权限
```javascript
{
  "read": true,
  "write": "get('database.users.${auth.openid}').role == 'admin'"
}
```

## 初始化数据

### 创建管理员账户

需要在 users 集合中手动创建一个管理员账户：

```javascript
{
  openid: "管理员的openid",
  nickName: "管理员",
  avatarUrl: "",
  role: "admin",
  phone: "",
  createTime: new Date(),
  updateTime: new Date()
}
```

### 示例轮播图数据

```javascript
[
  {
    imageUrl: "cloud://your-env.xxx/banner1.jpg",
    title: "欢迎来到智慧养老社区",
    linkUrl: "",
    sort: 1,
    status: "active",
    createTime: new Date()
  },
  {
    imageUrl: "cloud://your-env.xxx/banner2.jpg",
    title: "优质的养老服务",
    linkUrl: "",
    sort: 2,
    status: "active",
    createTime: new Date()
  }
]
```

### 示例活动数据

```javascript
[
  {
    title: "社区环境参观",
    description: "欢迎参观我们的社区环境，了解我们的设施和服务",
    type: "visit",
    imageUrl: "cloud://your-env.xxx/activity1.jpg",
    houseIntro: "宽敞明亮的房间，配备现代化设施",
    environmentIntro: "优美的园林环境，空气清新",
    serviceIntro: "24小时专业护理服务",
    location: "北京市朝阳区xxx街道xxx号",
    phone: "************",
    status: "published",
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    title: "健康养生讲座",
    description: "专业医师为您讲解健康养生知识",
    type: "activity",
    imageUrl: "cloud://your-env.xxx/activity2.jpg",
    houseIntro: "",
    environmentIntro: "",
    serviceIntro: "",
    location: "社区活动中心",
    phone: "************",
    status: "published",
    createTime: new Date(),
    updateTime: new Date()
  }
]
```

### 示例文章数据

```javascript
[
  {
    title: "社区生活精彩瞬间",
    content: "<p>今天社区举办了丰富多彩的活动...</p>",
    imageUrl: "cloud://your-env.xxx/article1.jpg",
    summary: "记录社区生活的美好时光",
    status: "published",
    createTime: new Date(),
    updateTime: new Date()
  }
]
```

## 部署步骤

1. **创建云开发环境**
   - 在微信开发者工具中创建云开发环境
   - 记录环境ID，更新到 app.js 中

2. **创建数据库集合**
   - 在云开发控制台创建上述5个集合
   - 设置相应的权限规则

3. **创建索引**
   - 为每个集合创建必要的索引以提高查询性能

4. **上传云函数**
   - 上传并部署4个云函数
   - 配置云函数的环境变量

5. **初始化数据**
   - 添加管理员账户
   - 添加示例数据（可选）

6. **测试功能**
   - 测试登录功能
   - 测试各项业务功能
   - 验证权限控制

## 注意事项

1. **安全性**
   - 确保权限规则正确设置
   - 定期检查数据访问日志

2. **性能优化**
   - 合理设置索引
   - 避免大量数据的全表扫描

3. **数据备份**
   - 定期备份重要数据
   - 设置数据恢复策略

4. **监控告警**
   - 设置云函数调用监控
   - 配置异常告警通知
