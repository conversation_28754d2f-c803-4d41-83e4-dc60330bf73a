<!--pages/user/detail/detail.wxml-->
<view class="detail-container">
  <!-- 主图轮播 -->
  <view class="image-section">
    <swiper
      class="detail-swiper"
      indicator-dots="{{images.length > 1}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#ffffff"
      autoplay="{{images.length > 1}}"
      interval="4000"
      duration="500"
      circular="{{images.length > 1}}"
    >
      <swiper-item wx:for="{{images}}" wx:key="index" class="swiper-item">
        <image
          src="{{item}}"
          class="detail-image"
          mode="aspectFill"
          lazy-load="true"
          bindtap="previewImage"
          data-current="{{item}}"
        />
      </swiper-item>
    </swiper>
  </view>

  <!-- 基本信息 -->
  <view class="info-section">
    <view class="title-area">
      <view class="main-title">{{detail.title}}</view>
      <view class="type-tag">
        浏览量：{{detail.viewCount || 0}}
      </view>
    </view>
    <view class="description">{{detail.description}}</view>
  </view>



  <!-- 位置信息 -->
  <view class="location-section" wx:if="{{detail.location}}">
    <image src="/images/401位置.png" style="width: 40rpx; height: 40rpx; margin-left: 30rpx;"></image>
    <view class="location-info" bindtap="openLocation">
      <view class="location-text">{{detail.location}}</view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section" wx:if="{{detail.phone}}">
    <image src="/images/联系方式电话.png" style="width: 40rpx; height: 40rpx; margin-left: 30rpx;"></image>
    <view class="contact-info" bindtap="makePhoneCall">
      <view class="contact-text">{{detail.phone}}</view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <view class="action-btn secondary" bindtap="showCustomerService">
    <image src="/images/咨询.png" class="btn-icon-1"></image>
    <text>客服咨询</text>
  </view>
  <view class="action-btn primary" bindtap="navigateToBooking">
    <image src="/images/预约.png" class="btn-icon-1"></image>
    <text>立即预约</text>
  </view>
</view>

<!-- 客服二维码弹窗 -->
<view class="customer-service-modal" wx:if="{{showServiceModal}}" bindtap="hideCustomerService">
  <view class="modal-content" catchtap>
    <view class="modal-header">
      <text class="modal-title">客服咨询</text>
      <view class="modal-close" bindtap="hideCustomerService">✕</view>
    </view>
    <view class="qr-code-container">
      <image
        src="/images/kefu.jpg"
        class="qr-code-image"
        mode="aspectFit"
        show-menu-by-longpress="{{true}}"
      />
      <text class="qr-code-tip">长按识别二维码咨询客服</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>