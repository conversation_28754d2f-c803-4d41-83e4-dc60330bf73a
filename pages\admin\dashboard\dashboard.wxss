/* pages/admin/dashboard/dashboard.wxss */
.dashboard-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  padding: 40rpx 30rpx;
  color: black;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.welcome-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.welcome-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

.welcome-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.welcome-time {
  font-size: 24rpx;
  opacity: 0.8;
  text-align: right;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: white;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 统计卡片区域 */
.stats-section {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.stats-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.stats-card {
  flex: 1;
  background: var(--surface-color);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-icon {
  font-size: 50rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.stats-icon.users {
  color: #4CAF50;
}

.stats-icon.reservations {
  color: var(--primary-color);
}

.stats-icon.active {
  color: #FF9800;
}

.stats-icon.activities {
  color: #9C27B0;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.stats-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 通用区域标题 */
.section-title {
  display: flex;
  align-items: center;
  padding: 0 30rpx 20rpx;
  position: relative;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.section-title text:nth-child(2) {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.more-link {
  font-size: 26rpx;
  color: var(--primary-color);
  padding: 8rpx 16rpx;
}

/* 快速操作区域 */
.quick-actions {
  background: var(--surface-color);
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0 30rpx;
}

.action-item {
  background: var(--background-color);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  border: 2rpx solid var(--divider-color);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.98);
  border-color: var(--primary-color);
}

.action-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 最近预约区域 */
.recent-section {
  background: var(--surface-color);
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
}

.recent-list {
  padding: 0 30rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid var(--divider-color);
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-info {
  flex: 1;
}

.recent-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.recent-user {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 6rpx;
}

.recent-time {
  font-size: 24rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

.recent-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.recent-status.active {
  background: #E8F5E8;
  color: #4CAF50;
}

.recent-status.cancelled {
  background: #FFEBEE;
  color: #F44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}