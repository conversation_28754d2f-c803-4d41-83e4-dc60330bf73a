// pages/user/profile/profile.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    stats: {
      total: 0,
      active: 0,
      completed: 0
    },
    showEditModal: false,
    editForm: {
      nickName: '',
      address: '',
      avatarUrl: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserData()
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      console.log('从存储加载的用户信息:', userInfo)
      
      if (!userInfo || !userInfo.openid) {
        // 跳转到登录页面，登录成功后返回个人中心
        wx.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      this.setData({ userInfo })
      console.log('设置到页面的用户信息:', this.data.userInfo)

      // 加载统计数据
      await this.loadStats()
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.openid) {
        return
      }

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'getUserReservations',
          userId: userInfo.openid
        }
      })

      if (res.result && res.result.code === 0) {
        const reservations = res.result.data

        // 计算统计数据（与reserved页面保持一致）
        const stats = {
          total: reservations.length,
          active: reservations.filter(item => item.status === 'active').length,
          completed: reservations.filter(item => item.status === 'cancelled').length
        }

        this.setData({ stats })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  /**
   * 编辑资料
   */
  editProfile() {
    console.log('当前用户信息:', this.data.userInfo)
    console.log('用户昵称:', this.data.userInfo.nickName)
    
    const editForm = {
      nickName: this.data.userInfo.nickName || '',
      address: this.data.userInfo.address || '',
      avatarUrl: this.data.userInfo.avatarUrl || ''
    }
    
    console.log('编辑表单:', editForm)
    
    this.setData({
      showEditModal: true,
      editForm: editForm
    })
  },

  /**
   * 关闭编辑弹窗
   */
  closeEditModal() {
    this.setData({ showEditModal: false })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  /**
   * 选择头像
   */
  async chooseAvatar() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })

      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        wx.showLoading({
          title: '上传中...'
        })

        // 上传图片到云存储
        const uploadRes = await wx.cloud.uploadFile({
          cloudPath: `avatars/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`,
          filePath: res.tempFilePaths[0]
        })

        if (uploadRes.fileID) {
          this.setData({
            'editForm.avatarUrl': uploadRes.fileID
          })

          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          })
        }
      }
    } catch (error) {
      console.error('选择头像失败:', error)
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 编辑表单输入
   */
  onEditInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    this.setData({
      [`editForm.${field}`]: value
    })
  },

  /**
   * 保存资料
   */
  async saveProfile() {
    const { nickName, address, avatarUrl } = this.data.editForm
    
    console.log('保存资料 - 昵称:', nickName)
    console.log('保存资料 - 地址:', address)
    console.log('保存资料 - 头像:', avatarUrl)

    if (!nickName || !nickName.trim()) {
      console.log('昵称验证失败 - nickName:', nickName)
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          action: 'updateProfile',
          profile: {
            nickName: nickName.trim(),
            address: address ? address.trim() : '',
            avatarUrl: avatarUrl || ''
          }
        }
      })

      console.log('云函数返回结果:', res.result)

      // 检查不同的返回格式
      if (res.result.success || res.result.code === 0) {
        // 更新本地存储
        const userInfo = { ...this.data.userInfo, ...res.result.data }
        wx.setStorageSync('userInfo', userInfo)
        app.globalData.userInfo = userInfo

        this.setData({
          userInfo,
          showEditModal: false
        })

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } else {
        throw new Error(res.result.message || '保存失败')
      }
    } catch (error) {
      console.error('保存资料失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 跳转到已预约页面
   */
  goToReserved(e) {
    const filter = e.currentTarget.dataset.filter
    const url = filter ? `/pages/user/reserved/reserved?filter=${filter}` : '/pages/user/reserved/reserved'
    wx.switchTab({
      url: '/pages/user/reserved/reserved'
    })
  },

  /**
   * 显示关于我们
   */
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '智慧养老小程序致力于为老年人提供专业、贴心的康养服务。我们拥有专业的护理团队和优美的康养环境，为您的健康生活保驾护航。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 显示设置
   */
  showSettings() {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示帮助
   */
  showHelp() {
    wx.showModal({
      title: '帮助与反馈',
      content: '如果您在使用过程中遇到任何问题，请联系我们的客服团队，我们将竭诚为您服务。\n\n客服电话：400-123-4567\n服务时间：周一至周日 8:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('userInfo')
          app.globalData.userInfo = null

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          // 跳转到登录页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserData().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})