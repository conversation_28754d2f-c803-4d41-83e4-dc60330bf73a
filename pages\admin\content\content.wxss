/* pages/admin/content/content.wxss */
.content-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 顶部操作栏 */
.header-actions {
  background: var(--surface-color);
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-switcher {
  display: flex;
  background: var(--background-color);
  border-radius: 8rpx;
  padding: 4rpx;
}

.tab-item {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: var(--primary-color);
  color: rgb(33,150,243);
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgb(33,150,243);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.add-icon {
  font-size: 36rpx;
  font-weight: 300;
}

/* 搜索区域 */
.search-section {
  padding: 0 30rpx 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: var(--surface-color);
  border-radius: 12rpx;
  padding: 0 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-secondary);
}

/* 内容列表 */
.content-list {
  padding: 0 20rpx;
}

.content-item {
  background: var(--surface-color);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.banner-image {
  width: 300rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.meta-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.meta-tag.visit {
  background: #4CAF50;
}

.meta-tag.activity {
  background: #FF9800;
}

.meta-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.meta-status.published,
.meta-status.active {
  background: #E8F5E8;
  color: #4CAF50;
}

.meta-status.draft,
.meta-status.inactive {
  background: #FFF3E0;
  color: #FF9800;
}

.meta-sort,
.meta-time {
  font-size: 22rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

.item-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx;
  gap: 12rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
}

.action-btn.edit {
  background: #E3F2FD;
  color: var(--primary-color);
}

.action-btn.delete {
  background: #FFEBEE;
  color: #F44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}