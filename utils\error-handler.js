// 错误处理工具类
const app = getApp();

/**
 * 错误类型枚举
 */
const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * 错误信息映射
 */
const ErrorMessages = {
  [ErrorTypes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ErrorTypes.AUTH_ERROR]: '登录已过期，请重新登录',
  [ErrorTypes.PERMISSION_ERROR]: '权限不足，无法执行此操作',
  [ErrorTypes.VALIDATION_ERROR]: '输入信息有误，请检查后重试',
  [ErrorTypes.BUSINESS_ERROR]: '操作失败，请稍后重试',
  [ErrorTypes.SYSTEM_ERROR]: '系统繁忙，请稍后重试',
  [ErrorTypes.UNKNOWN_ERROR]: '未知错误，请联系客服'
};

/**
 * 云函数错误处理
 * @param {Object} error 错误对象
 * @param {string} operation 操作名称
 * @returns {Object} 处理后的错误信息
 */
const handleCloudFunctionError = (error, operation = '操作') => {
  console.error(`${operation}失败:`, error);
  
  let errorType = ErrorTypes.UNKNOWN_ERROR;
  let errorMessage = ErrorMessages[ErrorTypes.UNKNOWN_ERROR];
  
  // 网络错误
  if (error.errCode === -1 || error.errMsg?.includes('network')) {
    errorType = ErrorTypes.NETWORK_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.NETWORK_ERROR];
  }
  // 权限错误
  else if (error.errCode === 401 || error.errMsg?.includes('permission')) {
    errorType = ErrorTypes.PERMISSION_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.PERMISSION_ERROR];
  }
  // 认证错误
  else if (error.errCode === 403 || error.errMsg?.includes('auth')) {
    errorType = ErrorTypes.AUTH_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.AUTH_ERROR];
  }
  // 业务错误（云函数返回的错误）
  else if (error.result && error.result.code !== 0) {
    errorType = ErrorTypes.BUSINESS_ERROR;
    errorMessage = error.result.message || ErrorMessages[ErrorTypes.BUSINESS_ERROR];
  }
  // 系统错误
  else if (error.errCode >= 500) {
    errorType = ErrorTypes.SYSTEM_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.SYSTEM_ERROR];
  }
  
  return {
    type: errorType,
    message: errorMessage,
    originalError: error
  };
};

/**
 * 数据库操作错误处理
 * @param {Object} error 错误对象
 * @param {string} operation 操作名称
 * @returns {Object} 处理后的错误信息
 */
const handleDatabaseError = (error, operation = '数据库操作') => {
  console.error(`${operation}失败:`, error);
  
  let errorType = ErrorTypes.UNKNOWN_ERROR;
  let errorMessage = ErrorMessages[ErrorTypes.UNKNOWN_ERROR];
  
  // 权限错误
  if (error.errCode === 'PERMISSION_DENIED') {
    errorType = ErrorTypes.PERMISSION_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.PERMISSION_ERROR];
  }
  // 网络错误
  else if (error.errCode === 'NETWORK_ERROR') {
    errorType = ErrorTypes.NETWORK_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.NETWORK_ERROR];
  }
  // 系统错误
  else {
    errorType = ErrorTypes.SYSTEM_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.SYSTEM_ERROR];
  }
  
  return {
    type: errorType,
    message: errorMessage,
    originalError: error
  };
};

/**
 * 文件上传错误处理
 * @param {Object} error 错误对象
 * @returns {Object} 处理后的错误信息
 */
const handleUploadError = (error) => {
  console.error('文件上传失败:', error);
  
  let errorType = ErrorTypes.UNKNOWN_ERROR;
  let errorMessage = '文件上传失败，请重试';
  
  // 文件大小超限
  if (error.errMsg?.includes('size')) {
    errorMessage = '文件大小超出限制';
  }
  // 文件格式不支持
  else if (error.errMsg?.includes('format')) {
    errorMessage = '文件格式不支持';
  }
  // 网络错误
  else if (error.errMsg?.includes('network')) {
    errorType = ErrorTypes.NETWORK_ERROR;
    errorMessage = ErrorMessages[ErrorTypes.NETWORK_ERROR];
  }
  // 存储空间不足
  else if (error.errMsg?.includes('storage')) {
    errorMessage = '存储空间不足';
  }
  
  return {
    type: errorType,
    message: errorMessage,
    originalError: error
  };
};

/**
 * 表单验证错误处理
 * @param {Object} validationResult 验证结果
 * @returns {Object} 处理后的错误信息
 */
const handleValidationError = (validationResult) => {
  return {
    type: ErrorTypes.VALIDATION_ERROR,
    message: validationResult.message || ErrorMessages[ErrorTypes.VALIDATION_ERROR],
    field: validationResult.field,
    originalError: validationResult
  };
};

/**
 * 统一错误处理函数
 * @param {Object} error 错误对象
 * @param {string} operation 操作名称
 * @param {boolean} showToast 是否显示提示
 * @returns {Object} 处理后的错误信息
 */
const handleError = (error, operation = '操作', showToast = true) => {
  let handledError;
  
  // 根据错误类型选择处理方式
  if (error.errCode !== undefined) {
    // 云函数或API错误
    handledError = handleCloudFunctionError(error, operation);
  } else if (error.code !== undefined) {
    // 数据库错误
    handledError = handleDatabaseError(error, operation);
  } else if (error.type === 'validation') {
    // 表单验证错误
    handledError = handleValidationError(error);
  } else {
    // 其他错误
    handledError = {
      type: ErrorTypes.UNKNOWN_ERROR,
      message: error.message || ErrorMessages[ErrorTypes.UNKNOWN_ERROR],
      originalError: error
    };
  }
  
  // 显示错误提示
  if (showToast) {
    app.showError(handledError.message);
  }
  
  // 记录错误日志
  logError(handledError, operation);
  
  return handledError;
};

/**
 * 记录错误日志
 * @param {Object} error 错误信息
 * @param {string} operation 操作名称
 */
const logError = (error, operation) => {
  const logData = {
    timestamp: new Date().toISOString(),
    operation: operation,
    errorType: error.type,
    errorMessage: error.message,
    originalError: error.originalError,
    userAgent: wx.getSystemInfoSync(),
    userId: app.globalData.userInfo?._id || 'anonymous'
  };
  
  // 在开发环境下打印详细日志
  if (wx.getAccountInfoSync().miniProgram.envVersion === 'develop') {
    console.group(`🚨 错误日志 - ${operation}`);
    console.error('错误类型:', error.type);
    console.error('错误信息:', error.message);
    console.error('原始错误:', error.originalError);
    console.error('完整日志:', logData);
    console.groupEnd();
  }
  
  // 可以在这里添加错误上报逻辑
  // reportErrorToServer(logData);
};

/**
 * 重试机制
 * @param {Function} fn 要重试的函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟（毫秒）
 * @returns {Promise} 执行结果
 */
const retry = async (fn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw error;
      }
      
      // 网络错误或系统错误才重试
      const handledError = handleError(error, '重试操作', false);
      if (handledError.type === ErrorTypes.NETWORK_ERROR || 
          handledError.type === ErrorTypes.SYSTEM_ERROR) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      } else {
        throw error;
      }
    }
  }
  
  throw lastError;
};

/**
 * 安全执行函数（捕获异常）
 * @param {Function} fn 要执行的函数
 * @param {string} operation 操作名称
 * @param {any} defaultValue 默认返回值
 * @returns {any} 执行结果或默认值
 */
const safeExecute = async (fn, operation = '操作', defaultValue = null) => {
  try {
    return await fn();
  } catch (error) {
    handleError(error, operation);
    return defaultValue;
  }
};

/**
 * 检查网络状态
 * @returns {Promise<boolean>} 网络是否可用
 */
const checkNetworkStatus = () => {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        resolve(res.networkType !== 'none');
      },
      fail: () => {
        resolve(false);
      }
    });
  });
};

/**
 * 网络状态监听
 */
const setupNetworkListener = () => {
  wx.onNetworkStatusChange((res) => {
    if (!res.isConnected) {
      app.showError('网络连接已断开');
    } else {
      app.showSuccess('网络连接已恢复');
    }
  });
};

module.exports = {
  ErrorTypes,
  ErrorMessages,
  handleCloudFunctionError,
  handleDatabaseError,
  handleUploadError,
  handleValidationError,
  handleError,
  logError,
  retry,
  safeExecute,
  checkNetworkStatus,
  setupNetworkListener
};
