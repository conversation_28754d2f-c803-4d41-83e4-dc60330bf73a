<!--pages/admin/reservations/reservations.wxml-->
<view class="reservations-container">
  <!-- 顶部统计 -->
  <view class="stats-header">
    <view class="stats-item">
      <view class="stats-number">{{stats.total}}</view>
      <view class="stats-label">总预约</view>
    </view>
    <view class="stats-item">
      <view class="stats-number active">{{stats.active}}</view>
      <view class="stats-label">进行中</view>
    </view>
    <view class="stats-item">
      <view class="stats-number cancelled">{{stats.cancelled}}</view>
      <view class="stats-label">已取消</view>
    </view>
    <view class="stats-item">
      <view class="stats-number today">{{stats.today}}</view>
      <view class="stats-label">今日预约</view>
    </view>
  </view>

  <!-- 筛选和搜索 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view
        class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="all"
      >
        全部
      </view>
      <view
        class="filter-tab {{currentFilter === 'active' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="active"
      >
        进行中
      </view>
      <view
        class="filter-tab {{currentFilter === 'cancelled' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="cancelled"
      >
        已取消
      </view>
      <view
        class="filter-tab {{currentFilter === 'today' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="today"
      >
        今日
      </view>
    </view>

    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索用户姓名或手机号"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="performSearch"
      />
      <view class="search-btn" bindtap="performSearch">
        <text>🔍</text>
      </view>
    </view>
  </view>

  <!-- 预约列表 -->
  <view class="reservations-list">
    <view
      class="reservation-item"
      wx:for="{{filteredReservations}}"
      wx:key="id"
      data-id="{{item._id}}"
    >
      <!-- 状态指示器 -->
      <view class="status-indicator {{item.status}}"></view>

      <!-- 预约内容 -->
      <view class="reservation-content">
        <!-- 用户信息 -->
        <view class="user-info">
          <view class="user-name">{{item.userName}}</view>
          <view class="user-phone">{{item.userPhone}}</view>
        </view>

        <!-- 服务信息 -->
        <view class="service-info">
          <view class="service-title">{{item.activityTitle || '康养服务'}}</view>
        </view>

        <!-- 预约时间 -->
        <view class="time-info">
          <text class="time-icon">📅</text>
          <text class="time-text">{{item.reserveDate}}</text>
        </view>

        <!-- 备注信息 -->
        <view class="remark-info" wx:if="{{item.remark}}">
          <text class="remark-icon">📝</text>
          <text class="remark-text">{{item.remark}}</text>
        </view>

        <!-- 取消原因 -->
        <view class="cancel-reason" wx:if="{{item.status === 'cancelled' && item.cancelReason}}">
          <text class="cancel-icon">❌</text>
          <text class="cancel-text">取消原因：{{item.cancelReason}}</text>
        </view>

        <!-- 创建时间 -->
        <view class="create-time">
          <text>预约时间：{{item.createTime}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="reservation-actions">
        <view class="status-badge {{item.status}}">
          {{item.status === 'active' ? '进行中' : '已取消'}}
        </view>
        <view class="action-buttons">
          <view
            class="action-btn cancel"
            wx:if="{{item.status === 'active'}}"
            bindtap="cancelReservation"
            catchtap
            data-id="{{item._id}}"
          >
            取消
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && filteredReservations.length === 0}}">
    <view class="empty-icon">📋</view>
    <view class="empty-title">暂无预约记录</view>
    <view class="empty-desc">
      {{currentFilter === 'all' ? '还没有任何预约记录' :
        currentFilter === 'active' ? '当前没有进行中的预约' :
        currentFilter === 'cancelled' ? '没有已取消的预约' : '今日没有预约'}}
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>