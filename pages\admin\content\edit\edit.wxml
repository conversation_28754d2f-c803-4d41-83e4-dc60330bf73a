<!--pages/admin/content/edit/edit.wxml-->
<view class="edit-container">
  <!-- 表单内容 -->
  <form bindsubmit="handleSubmit" bindreset="handleReset">
    <view class="form-section">
      <!-- 内容类型显示 -->
      <view class="type-indicator">
        <text class="type-text">{{typeText}}</text>
      </view>

      <!-- 标题输入 -->
      <view class="form-item">
        <view class="form-label required">标题</view>
        <input
          class="form-input"
          placeholder="请输入{{typeText}}标题"
          value="{{formData.title}}"
          bindinput="onTitleInput"
          maxlength="50"
        />
        <view class="char-count">{{formData.title.length}}/50</view>
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <view class="form-label required">图片 (最多8张)</view>

        <!-- 主图显示 -->
        <view wx:if="{{formData.imageUrl}}" class="main-image-section">
          <view class="main-image-label">主图</view>
          <view class="image-preview main-image">
            <image src="{{formData.imageUrl}}" mode="aspectFill" />
            <view class="image-actions">
              <view class="action-btn" bindtap="previewImage" data-url="{{formData.imageUrl}}">预览</view>
              <view class="action-btn delete" bindtap="removeImage">删除</view>
            </view>
          </view>
        </view>

        <!-- 多图片列表 -->
        <view wx:if="{{formData.images.length > 0}}" class="images-grid">
          <view class="images-grid-label">所有图片</view>
          <view class="images-list">
            <view
              wx:for="{{formData.images}}"
              wx:key="index"
              class="image-item {{formData.imageUrl === item ? 'is-main' : ''}}"
            >
              <image src="{{item}}" mode="aspectFill" class="grid-image" />
              <view class="image-overlay">
                <view class="image-actions-grid">
                  <view class="action-btn-small" bindtap="previewImage" data-url="{{item}}">预览</view>
                  <view wx:if="{{formData.imageUrl !== item}}" class="action-btn-small" bindtap="setAsMainImage" data-url="{{item}}">设为主图</view>
                  <view class="action-btn-small delete" bindtap="removeImageFromList" data-index="{{index}}" data-url="{{item}}">删除</view>
                </view>
              </view>
              <view wx:if="{{formData.imageUrl === item}}" class="main-badge">主图</view>
            </view>
          </view>
        </view>

        <!-- 上传按钮 -->
        <view class="upload-section">
          <view class="upload-placeholder" bindtap="chooseImage">
            <text class="upload-icon">📷</text>
            <text class="upload-text">{{formData.images.length === 0 ? '点击上传图片' : '继续添加图片'}}</text>
            <text class="upload-tip">{{formData.images.length}}/8</text>
          </view>
        </view>
      </view>

      <!-- 活动特有字段 -->
      <block wx:if="{{contentType === 'activity'}}">
        <!-- 活动类型 -->
        <view class="form-item">
          <view class="form-label required">活动类型</view>
          <view class="radio-group">
            <label class="radio-item">
              <radio
                value="visit"
                checked="{{formData.type === 'visit'}}"
                bindtap="onTypeChange"
                data-type="visit"
              />
              <text>参观</text>
            </label>
            <label class="radio-item">
              <radio
                value="activity"
                checked="{{formData.type === 'activity'}}"
                bindtap="onTypeChange"
                data-type="activity"
              />
              <text>活动</text>
            </label>
          </view>
        </view>

        <!-- 活动描述 -->
        <view class="form-item">
          <view class="form-label required">活动描述</view>
          <textarea
            class="form-textarea"
            placeholder="请输入活动描述"
            value="{{formData.description}}"
            bindinput="onDescriptionInput"
            maxlength="200"
          />
          <view class="char-count">{{formData.description.length}}/200</view>
        </view>



        <!-- 位置信息 -->
        <view class="form-item">
          <view class="form-label">位置信息</view>
          <input
            class="form-input"
            placeholder="请输入位置信息"
            value="{{formData.location}}"
            bindinput="onLocationInput"
          />
        </view>

        <!-- 联系电话 -->
        <view class="form-item">
          <view class="form-label">联系电话</view>
          <input
            class="form-input"
            placeholder="请输入联系电话"
            value="{{formData.phone}}"
            bindinput="onPhoneInput"
            type="number"
          />
        </view>

        <!-- 浏览量 -->
        <view class="form-item">
          <view class="form-label">浏览量</view>
          <input
            class="form-input"
            placeholder="请输入浏览量"
            value="{{formData.viewCount}}"
            bindinput="onViewCountInput"
            type="number"
          />
        </view>

        <!-- 所属地区 -->
        <view class="form-item">
          <view class="form-label">所属地区</view>
          <picker class="form-picker" mode="region" bindchange="onRegionChange" value="{{selectedRegion}}">
            <view class="picker-display">
              <text class="picker-text">{{regionText}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </block>

      <!-- 文章特有字段 -->
      <block wx:if="{{contentType === 'article'}}">
        <!-- 文章摘要 -->
        <view class="form-item">
          <view class="form-label required">文章摘要</view>
          <textarea
            class="form-textarea"
            placeholder="请输入文章摘要"
            value="{{formData.summary}}"
            bindinput="onSummaryInput"
            maxlength="100"
          />
          <view class="char-count">{{formData.summary.length}}/100</view>
        </view>

        <!-- 文章内容 -->
        <view class="form-item">
          <view class="form-label required">文章内容</view>

          <!-- 富文本编辑器工具栏 -->
          <view class="editor-toolbar">
            <!-- 第一行：基础格式化和字体大小 -->
            <view class="toolbar-row">
              <view class="toolbar-group">
                <button class="toolbar-btn {{formats.bold ? 'active' : ''}}" bindtap="formatText" data-type="bold">
                  <text class="iconfont">B</text>
                </button>
                <button class="toolbar-btn {{formats.italic ? 'active' : ''}}" bindtap="formatText" data-type="italic">
                  <text class="iconfont">I</text>
                </button>
                <button class="toolbar-btn {{formats.underline ? 'active' : ''}}" bindtap="formatText" data-type="underline">
                  <text class="iconfont">U</text>
                </button>
                <button class="toolbar-btn {{formats.strike ? 'active' : ''}}" bindtap="formatText" data-type="strike">
                  <text class="iconfont">S</text>
                </button>
              </view>

              <view class="toolbar-group">
                <button class="toolbar-btn" bindtap="setFontSize" data-size="14px">14</button>
                <button class="toolbar-btn" bindtap="setFontSize" data-size="16px">16</button>
                <button class="toolbar-btn" bindtap="setFontSize" data-size="18px">18</button>
                <button class="toolbar-btn" bindtap="setFontSize" data-size="20px">20</button>
              </view>

              <view class="toolbar-group">
                <button class="toolbar-btn" bindtap="undo">
                  <text class="iconfont">↶</text>
                </button>
                <button class="toolbar-btn" bindtap="redo">
                  <text class="iconfont">↷</text>
                </button>
              </view>
            </view>

            <!-- 第二行：标题、对齐和列表 -->
            <view class="toolbar-row">
              <view class="toolbar-group">
                <button class="toolbar-btn" bindtap="formatText" data-type="header" data-level="1">H1</button>
                <button class="toolbar-btn" bindtap="formatText" data-type="header" data-level="2">H2</button>
                <button class="toolbar-btn" bindtap="formatText" data-type="header" data-level="3">H3</button>
              </view>

              <view class="toolbar-group">
                <button class="toolbar-btn {{formats.align === 'left' ? 'active' : ''}}" bindtap="formatText" data-type="align" data-align="left">
                  <text class="iconfont">←</text>
                </button>
                <button class="toolbar-btn {{formats.align === 'center' ? 'active' : ''}}" bindtap="formatText" data-type="align" data-align="center">
                  <text class="iconfont">↔</text>
                </button>
                <button class="toolbar-btn {{formats.align === 'right' ? 'active' : ''}}" bindtap="formatText" data-type="align" data-align="right">
                  <text class="iconfont">→</text>
                </button>
              </view>

              <view class="toolbar-group">
                <button class="toolbar-btn" bindtap="formatText" data-type="list" data-list-type="ordered">
                  <text class="iconfont">1.</text>
                </button>
                <button class="toolbar-btn" bindtap="formatText" data-type="list" data-list-type="bullet">
                  <text class="iconfont">•</text>
                </button>
              </view>
            </view>

            <!-- 第三行：插入功能 -->
            <view class="toolbar-row">
              <view class="toolbar-group">
                <button class="toolbar-btn" bindtap="insertImageToEditor" disabled="{{imageUploading}}">
                  <text class="iconfont">📷</text>
                </button>
                <button class="toolbar-btn" bindtap="insertLink">
                  <text class="iconfont">🔗</text>
                </button>
                <button class="toolbar-btn" bindtap="insertTable">
                  <text class="iconfont">📊</text>
                </button>
              </view>
            </view>
          </view>

          <!-- 富文本编辑器 -->
          <editor
            id="editor"
            class="rich-editor"
            placeholder="请输入文章内容..."
            bindready="onEditorReady"
            bindinput="onEditorInput"
            bindfocus="onEditorFocus"
            bindblur="onEditorBlur"
            bindstatuschange="onStatusChange"
            show-img-size
            show-img-toolbar
            show-img-resize
          ></editor>

          <!-- 字数统计 -->
          <view class="word-count">字数：{{wordCount}}</view>
        </view>
      </block>

      <!-- 轮播图特有字段 -->
      <block wx:if="{{contentType === 'banner'}}">

        <!-- 排序 -->
        <view class="form-item">
          <view class="form-label required">排序</view>
          <input
            class="form-input"
            placeholder="请输入排序数字"
            value="{{formData.sort}}"
            bindinput="onSortInput"
            type="number"
          />
          <view class="form-tip">数字越小排序越靠前</view>
        </view>
      </block>

      <!-- 状态选择 -->
      <view class="form-item">
        <view class="form-label">状态</view>
        <view class="radio-group">
          <label class="radio-item" wx:if="{{contentType !== 'banner'}}">
            <radio
              value="draft"
              checked="{{formData.status === 'draft'}}"
              bindtap="onStatusChange"
              data-status="draft"
            />
            <text>草稿</text>
          </label>
          <label class="radio-item">
            <radio
              value="{{contentType === 'banner' ? 'active' : 'published'}}"
              checked="{{formData.status === (contentType === 'banner' ? 'active' : 'published')}}"
              bindtap="onStatusChange"
              data-status="{{contentType === 'banner' ? 'active' : 'published'}}"
            />
            <text>{{contentType === 'banner' ? '启用' : '发布'}}</text>
          </label>
          <label class="radio-item" wx:if="{{contentType === 'banner'}}">
            <radio
              value="inactive"
              checked="{{formData.status === 'inactive'}}"
              bindtap="onStatusChange"
              data-status="inactive"
            />
            <text>禁用</text>
          </label>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn btn-cancel" bindtap="handleCancel">取消</button>
      <button class="btn btn-submit" formType="submit" loading="{{isSubmitting}}">
        {{isEdit ? '更新' : '创建'}}
      </button>
    </view>
  </form>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{isLoading}}">
  <view class="loading-content">
    <text>加载中...</text>
  </view>
</view>
