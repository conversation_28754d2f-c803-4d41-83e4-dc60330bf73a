<!--pages/user/article/article.wxml-->
<view class="article-container">
  <!-- 文章头部 -->
  <view class="article-header">
    <!-- 主图 -->
    <view class="article-image" wx:if="{{article.imageUrl}}">
      <image src="{{article.imageUrl}}" mode="aspectFill" lazy-load="true" />
    </view>
    
    <!-- 文章标题 -->
    <view class="article-title">{{article.title}}</view>
    
    <!-- 文章信息 -->
    <view class="article-meta">
      <view class="meta-item">
        <text class="meta-icon">📅</text>
        <text class="meta-text">{{article.createTime}}</text>
      </view>
      <view class="meta-item" wx:if="{{article.updateTime !== article.createTime}}">
        <text class="meta-icon">✏️</text>
        <text class="meta-text">更新于 {{article.updateTime}}</text>
      </view>
    </view>
    
    <!-- 文章摘要 -->
    <view class="article-summary" wx:if="{{article.summary}}">
      {{article.summary}}
    </view>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <rich-text 
      nodes="{{article.content}}" 
      bindtap="onRichTextTap"
      space="nbsp"
    ></rich-text>
  </view>

  <!-- 分享按钮 -->
  <view class="article-actions">
    <button class="share-btn" bindtap="shareArticle" open-type="share">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享文章</text>
    </button>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{false}}">
    <view class="section-title">相关推荐</view>
    <!-- 这里可以添加相关文章推荐 -->
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{isLoading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 空状态 -->
<view class="empty-state" wx:if="{{!isLoading && !article}}">
  <view class="empty-icon">📄</view>
  <view class="empty-text">文章不存在或已被删除</view>
  <button class="empty-btn" bindtap="navigateBack">返回</button>
</view>
