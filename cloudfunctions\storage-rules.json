{"rules": {"articles": {"images": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}}, "activities": {"images": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}}, "banners": {"images": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}}, "content": {"article": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}, "activity": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}, "banner": {"read": true, "write": "auth != null && get('database.users.${auth.openid}').role == 'admin'"}}, "images": {"read": true, "write": "auth != null"}}}