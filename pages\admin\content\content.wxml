<!--pages/admin/content/content.wxml-->
<view class="content-container">
  <!-- 顶部操作栏 -->
  <view class="header-actions">
    <view class="tab-switcher">
      <view
        class="tab-item {{currentTab === 'activities' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="activities"
      >
        活动管理
      </view>
      <view
        class="tab-item {{currentTab === 'articles' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="articles"
      >
        文章管理
      </view>
      <view
        class="tab-item {{currentTab === 'banners' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="banners"
      >
        轮播图管理
      </view>
    </view>
    <view class="add-btn" bindtap="addContent">
      <text class="add-icon">+</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索{{currentTab === 'activities' ? '活动' : currentTab === 'articles' ? '文章' : '轮播图'}}"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="performSearch"
      />
      <view class="search-btn" bindtap="performSearch">
        <text>🔍</text>
      </view>
    </view>
  </view>

  <!-- 内容列表 -->
  <view class="content-list">
    <!-- 活动列表 -->
    <view wx:if="{{currentTab === 'activities'}}">
      <view
        class="content-item"
        wx:for="{{activities}}"
        wx:key="id"
        bindtap="editContent"
        data-id="{{item._id}}"
        data-type="activity"
      >
        <image src="{{item.imageUrl}}" class="item-image" mode="aspectFill" />
        <view class="item-content">
          <view class="item-title">{{item.title}}</view>
          <view class="item-desc">{{item.description}}</view>
          <view class="item-meta">
            <text class="meta-tag {{item.type}}">{{item.type === 'visit' ? '参观' : '活动'}}</text>
            <text class="meta-status {{item.status}}">{{item.status === 'published' ? '已发布' : '草稿'}}</text>
            <text class="meta-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="item-actions">
          <view
            class="action-btn edit"
            bindtap="editContent"
            catchtap
            data-id="{{item._id}}"
            data-type="activity"
          >
            编辑
          </view>
          <view
            class="action-btn delete"
            bindtap="deleteContent"
            catchtap
            data-id="{{item._id}}"
            data-type="activity"
          >
            删除
          </view>
        </view>
      </view>
    </view>

    <!-- 文章列表 -->
    <view wx:if="{{currentTab === 'articles'}}">
      <view
        class="content-item"
        wx:for="{{articles}}"
        wx:key="id"
        bindtap="editContent"
        data-id="{{item._id}}"
        data-type="article"
      >
        <image src="{{item.imageUrl}}" class="item-image" mode="aspectFill" />
        <view class="item-content">
          <view class="item-title">{{item.title}}</view>
          <view class="item-desc">{{item.summary}}</view>
          <view class="item-meta">
            <text class="meta-status {{item.status}}">{{item.status === 'published' ? '已发布' : '草稿'}}</text>
            <text class="meta-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="item-actions">
          <view
            class="action-btn edit"
            bindtap="editContent"
            catchtap
            data-id="{{item._id}}"
            data-type="article"
          >
            编辑
          </view>
          <view
            class="action-btn delete"
            bindtap="deleteContent"
            catchtap
            data-id="{{item._id}}"
            data-type="article"
          >
            删除
          </view>
        </view>
      </view>
    </view>

    <!-- 轮播图列表 -->
    <view wx:if="{{currentTab === 'banners'}}">
      <view
        class="content-item banner-item"
        wx:for="{{banners}}"
        wx:key="id"
        bindtap="editContent"
        data-id="{{item._id}}"
        data-type="banner"
      >
        <image src="{{item.imageUrl}}" class="banner-image" mode="aspectFill" />
        <view class="item-content">
          <view class="item-title">{{item.title}}</view>
          <view class="item-meta">
            <text class="meta-sort">排序: {{item.sort}}</text>
            <text class="meta-status {{item.status}}">{{item.status === 'active' ? '启用' : '禁用'}}</text>
            <text class="meta-time">{{item.createTime}}</text>
          </view>
        </view>
        <view class="item-actions">
          <view
            class="action-btn delete"
            bindtap="deleteContent"
            catchtap
            data-id="{{item._id}}"
            data-type="banner"
          >
            删除
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && getCurrentList().length === 0}}">
    <view class="empty-icon">📝</view>
    <view class="empty-title">暂无内容</view>
    <view class="empty-desc">点击右上角添加按钮创建新内容</view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>