// pages/user/article/article.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    article: null,
    articleId: '',
    
    // 分享相关
    shareTitle: '',
    sharePath: '',
    
    // 图片预览
    imageUrls: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options
    if (id) {
      this.setData({ articleId: id })
      this.loadArticleDetail(id)
    } else {
      wx.showToast({
        title: '文章ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 加载文章详情
   */
  async loadArticleDetail(id) {
    try {
      this.setData({ isLoading: true })

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getArticleDetail',
          id
        }
      })

      if (res.result && res.result.code === 0) {
        const article = res.result.data

        // 直接使用文章内容，不进行图片链接转换
        const imageUrls = this.extractImageUrls(article.content)

        this.setData({
          article: {
            ...article,
            createTime: this.formatTime(article.createTime),
            updateTime: this.formatTime(article.updateTime)
          },
          imageUrls,
          shareTitle: article.title,
          sharePath: `/pages/user/article/article?id=${id}`,
          isLoading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: article.title
        })
      } else {
        throw new Error(res.result?.message || '获取文章详情失败')
      }
    } catch (error) {
      console.error('加载文章详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
  },



  /**
   * 从HTML内容中提取图片URL
   */
  extractImageUrls(htmlContent) {
    if (!htmlContent) return []

    const imgRegex = /<img[^>]+src="([^"]+)"/g
    const urls = []
    let match

    while ((match = imgRegex.exec(htmlContent)) !== null) {
      // 解码HTML实体，确保URL格式正确
      const decodedUrl = match[1].replace(/&amp;/g, '&')
      urls.push(decodedUrl)
    }

    return urls
  },

  /**
   * 处理富文本中的图片点击
   */
  onRichTextTap(e) {
    // 如果点击的是图片，进行预览
    if (e.detail.src) {
      wx.previewImage({
        current: e.detail.src,
        urls: this.data.imageUrls
      })
    }
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    if (!date) return ''

    const now = new Date()
    const target = new Date(date)
    const diff = now - target

    const minute = 60 * 1000
    const hour = minute * 60
    const day = hour * 24

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < day * 7) {
      return Math.floor(diff / day) + '天前'
    } else {
      return target.toLocaleDateString()
    }
  },

  /**
   * 分享文章
   */
  shareArticle() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadArticleDetail(this.data.articleId).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.shareTitle || '智慧养老 - 精彩文章',
      path: this.data.sharePath || '/pages/user/home/<USER>',
      imageUrl: this.data.article?.imageUrl || ''
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: this.data.shareTitle || '智慧养老 - 精彩文章',
      imageUrl: this.data.article?.imageUrl || ''
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  }
})
