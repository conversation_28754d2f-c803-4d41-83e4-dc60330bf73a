/* pages/user/home/<USER>/
.home-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  margin-bottom: 30rpx;
}

.banner-swiper {
  height: 400rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 60rpx 40rpx 40rpx;
}

.banner-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 顶部TAB */
.tab-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 50px;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  position: relative;
}

.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #666666;
}

.tab-item.active .tab-text {
  color: rgb(33,150,243);
  font-weight: bold;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: rgb(33,150,243);
  transition: all 0.3s ease;
}

/* 通用区域标题 */
.section-title {
  display: flex;
  align-items: center;
  padding: 0 30rpx 20rpx;
  position: relative;
}

.title-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.more-link {
  font-size: 28rpx;
  color: var(--primary-color);
  padding: 8rpx 16rpx;
}

/* 快速预约区域 */
.quick-booking {
  margin-bottom: 40rpx;
}

.booking-cards {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx;
}

.booking-card {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
}

.card-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.card-desc {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 服务特色区域 */
.features-section {
  margin-bottom: 40rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0 30rpx;
}

.feature-item {
  background: var(--surface-color);
  border-radius: 20rpx;
  padding: 30rpx 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.feature-icon {
  font-size: 50rpx;
  margin-bottom: 16rpx;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 热门活动区域 */
.activities-section {
  margin-bottom: 40rpx;
}

.activities-list {
  padding: 0 30rpx;
}

.activity-item {
  background: var(--surface-color);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.activity-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right: 80rpx;
}

.activity-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.activity-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.activity-type {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  margin-left: 12rpx;
  flex-shrink: 0;
}

.activity-type.visit {
  background: #4CAF50;
}

.activity-type.activity {
  background: #FF9800;
}

.activity-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.activity-location {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.meta-icon {
  margin-right: 4rpx;
}

.activity-time {
  flex-shrink: 0;
}

.activity-action {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
}

.action-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: black;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.empty-activities {
  text-align: center;
  padding: 80rpx 30rpx;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 最新动态区域 */
.articles-section {
  margin-bottom: 40rpx;
}

.articles-list {
  padding: 0 30rpx;
}

.article-item {
  background: var(--surface-color);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.article-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.article-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-summary {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-time {
  font-size: 24rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 联系我们区域 */
.contact-section {
  margin-bottom: 40rpx;
}

.contact-info {
  padding: 0 30rpx;
}

.contact-item {
  background: var(--surface-color);
  border-radius: 20rpx;
  padding: 30rpx 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 6rpx;
}

.contact-value {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.contact-arrow {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-left: 16rpx;
}

/* Tab 内容区域 */
.tab-content {
  margin-top: 30rpx;
}

.tab-panel {
  min-height: 400rpx;
}

/* 小型加载状态 */
.loading-mini {
  text-align: center;
  padding: 60rpx 40rpx;
  color: var(--text-secondary);
  font-size: 26rpx;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 30rpx;
  color: var(--text-tertiary);
}

.empty-state .empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-state .empty-text {
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: var(--surface-color);
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  margin: 0 30rpx 20rpx;
}

.filter-label {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-right: 20rpx;
  white-space: nowrap;
}

.region-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: var(--background-color);
  border-radius: 12rpx;
  border: 1rpx solid var(--border-color);
}

.picker-text {
  font-size: 28rpx;
  color: var(--text-primary);
}

.picker-arrow {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-left: 10rpx;
}