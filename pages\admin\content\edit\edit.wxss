/* pages/admin/content/edit/edit.wxss */
.edit-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 类型指示器 */
.type-indicator {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  border-radius: 12rpx;
}

.type-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #ff4444;
  margin-left: 8rpx;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2196F3;
  background: white;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea.large {
  min-height: 300rpx;
}

.form-textarea:focus {
  border-color: #2196F3;
  background: white;
}

/* 字符计数 */
.char-count {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #333;
}

/* 图片上传 */
.image-upload {
  width: 100%;
}

.upload-placeholder {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #ccc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.action-btn.delete {
  background: rgba(255, 68, 68, 0.8);
}

/* 表单提示 */
.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 操作按钮 */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 40rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-submit {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.btn-submit:disabled {
  background: #ccc;
}

/* 富文本编辑器样式 */
.editor-toolbar {
  border: 2rpx solid #e0e0e0;
  border-bottom: none;
  border-radius: 8rpx 8rpx 0 0;
  background: #fafafa;
  padding: 12rpx 8rpx;
  overflow-x: auto;
}

.toolbar-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 8rpx;
  flex-wrap: nowrap;
  min-width: 100%;
}

.toolbar-row:last-child {
  margin-bottom: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.toolbar-btn {
  width: 48rpx;
  height: 48rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #333;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
  min-width: 48rpx;
}

.toolbar-btn:active {
  background: #f0f0f0;
  transform: scale(0.95);
}

.toolbar-btn.active {
  background: #2196F3;
  color: white;
  border-color: #2196F3;
}

.toolbar-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  border-color: #eee;
}

.iconfont {
  font-style: normal;
  font-weight: bold;
  font-size: 18rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .toolbar-btn {
    width: 44rpx;
    height: 44rpx;
    min-width: 44rpx;
    font-size: 18rpx;
  }

  .iconfont {
    font-size: 16rpx;
  }

  .toolbar-group {
    gap: 3rpx;
    margin-right: 8rpx;
  }

  .editor-toolbar {
    padding: 8rpx 6rpx;
  }
}

.rich-editor {
  width: 100%;
  min-height: 400rpx;
  border: 2rpx solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
  padding: 20rpx;
  background: white;
  box-sizing: border-box;
}

.rich-editor:focus {
  border-color: #2196F3;
}

.word-count {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 40rpx 60rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}

/* 选择器样式 */
.form-picker {
  width: 100%;
}

.picker-display {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
}

/* 多图片上传样式 */
.main-image-section {
  margin-bottom: 20rpx;
}

.main-image-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.main-image {
  border: 2rpx solid #2196F3;
}

.images-grid {
  margin-top: 20rpx;
}

.images-grid-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.images-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.image-item {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
}

.image-item.is-main {
  border-color: #2196F3;
}

.grid-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:active .image-overlay {
  opacity: 1;
}

.image-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: center;
}

.action-btn-small {
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 20rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.action-btn-small.delete {
  background: rgba(255, 68, 68, 0.9);
  color: white;
}

.main-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: #2196F3;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.upload-section {
  margin-top: 20rpx;
}

.upload-tip {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}
