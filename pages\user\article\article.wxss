/* pages/user/article/article.wxss */
.article-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 文章头部 */
.article-header {
  background: white;
  margin-bottom: 20rpx;
}

.article-image {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.article-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  padding: 40rpx 30rpx 20rpx;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 30rpx;
  padding: 0 30rpx 20rpx;
  font-size: 26rpx;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.article-summary {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 文章内容 */
.article-content {
  background: white;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
}

/* 富文本内容样式 */
.article-content rich-text {
  word-break: break-word;
}

/* 文章操作 */
.article-actions {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.share-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
}

.share-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-weight: 500;
}

/* 相关推荐 */
.related-section {
  background: white;
  padding: 40rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .article-title {
    font-size: 36rpx;
    padding: 30rpx 20rpx 16rpx;
  }
  
  .article-content {
    padding: 30rpx 20rpx;
    font-size: 28rpx;
  }
  
  .article-meta {
    padding: 0 20rpx 16rpx;
    font-size: 24rpx;
  }
  
  .article-summary {
    margin: 0 20rpx 20rpx;
    padding: 20rpx;
    font-size: 26rpx;
  }
}
