/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 蓝色主题色彩变量 */
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #BBDEFB;
  --accent-color: #03DAC6;
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --divider-color: #e0e0e0;
}

/* 通用容器 */
.container {
  padding: 20rpx;
  background-color: var(--background-color);
  min-height: 100vh;
}

.card {
  background-color: var(--surface-color);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: 8rpx;
  padding: 22rpx 46rpx;
  font-size: 32rpx;
}

.btn-secondary:active {
  background-color: var(--primary-light);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
  font-size: 32rpx;
}

.text-secondary {
  color: var(--text-secondary);
  font-size: 28rpx;
}

.text-caption {
  color: var(--text-secondary);
  font-size: 24rpx;
}

.text-title {
  color: var(--text-primary);
  font-size: 36rpx;
  font-weight: 500;
}

.text-headline {
  color: var(--text-primary);
  font-size: 40rpx;
  font-weight: 600;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--divider-color);
  margin: 20rpx 0;
}

/* 图片样式 */
.image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 列表项 */
.list-item {
  background-color: var(--surface-color);
  padding: 24rpx;
  border-bottom: 1rpx solid var(--divider-color);
}

.list-item:last-child {
  border-bottom: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
}

.empty-state .icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-secondary);
}

/* 标签 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.tag.success {
  background-color: #C8E6C9;
  color: #2E7D32;
}

.tag.warning {
  background-color: #FFE0B2;
  color: #F57C00;
}

.tag.error {
  background-color: #FFCDD2;
  color: #C62828;
}
