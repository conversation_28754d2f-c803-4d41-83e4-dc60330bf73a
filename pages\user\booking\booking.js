﻿// pages/user/booking/booking.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    isSubmitting: false,
    activityId: '',
    activity: null,
    
    // 表单数据
    formData: {
      userName: '',
      userPhone: '',
      reserveDate: '',
      remark: ''
    },

    // 日期时间限制
    minDate: '',
    maxDate: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { activityId } = options
    
    if (!activityId) {
      wx.showToast({
        title: '活动ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ activityId })
    this.initDateRange()
    this.loadUserInfo()
    this.loadActivityDetail()
  },

  /**
   * 初始化日期范围
   */
  initDateRange() {
    const today = new Date()
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
    const maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)

    this.setData({
      minDate: this.formatDate(tomorrow),
      maxDate: this.formatDate(maxDate)
    })
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        'formData.userName': userInfo.nickName || '',
        'formData.userPhone': userInfo.phone || ''
      })
    }
  },

  /**
   * 加载活动详情
   */
  async loadActivityDetail() {
    try {
      this.setData({ isLoading: true })

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getActivityDetail',
          id: this.data.activityId
        }
      })

      if (res.result && res.result.code === 0) {
        const activity = res.result.data
        
        this.setData({
          activity,
          isLoading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `预约 - ${activity.title}`
        })
      } else {
        throw new Error(res.result?.message || '获取活动详情失败')
      }
    } catch (error) {
      console.error('加载活动详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    this.setData({
      'formData.reserveDate': e.detail.value
    })
  },



  /**
   * 表单验证
   */
  validateForm() {
    const { userName, userPhone, reserveDate } = this.data.formData

    if (!userName.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return false
    }

    if (!userPhone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }

    if (!/^1[3-9]\d{9}$/.test(userPhone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }

    if (!reserveDate) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 提交预约
   */
  async submitBooking() {
    if (this.data.isSubmitting) return

    if (!this.validateForm()) return

    try {
      this.setData({ isSubmitting: true })

      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.openid) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }, 1500)
        return
      }

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'createReservation',
          userId: userInfo.openid,
          activityId: this.data.activityId,
          userName: this.data.formData.userName.trim(),
          userPhone: this.data.formData.userPhone.trim(),
          reserveDate: this.data.formData.reserveDate,
          remark: this.data.formData.remark.trim()
        }
      })

      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '预约成功',
          icon: 'success'
        })

        // 延迟跳转到已预约页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/user/reserved/reserved'
          })
        }, 1500)
      } else {
        throw new Error(res.result?.message || '预约失败')
      }
    } catch (error) {
      console.error('提交预约失败:', error)
      wx.showToast({
        title: error.message || '预约失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isSubmitting: false })
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  }
})
