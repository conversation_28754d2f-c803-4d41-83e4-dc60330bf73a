// pages/user/home/<USER>
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    banners: [],
    activities: [],
    articles: [],
    // Tab 相关数据
    currentTab: 'visit',
    indicatorLeft: 0,
    indicatorWidth: 0,
    // 各 Tab 的数据
    visitActivities: [],
    communityActivities: [],
    liveArticles: [],
    // 各 Tab 的加载状态
    visitLoading: false,
    activityLoading: false,
    liveLoading: false,
    // 地区筛选相关
    selectedVisitRegion: ['全部地区'],
    selectedActivityRegion: ['全部地区'],
    visitRegionText: '全部地区',
    activityRegionText: '全部地区'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadPageData()
    this.initTabIndicator()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时不强制检查登录状态，允许游客浏览
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    try {
      this.setData({ isLoading: true })

      // 并行加载轮播图、活动和文章数据
      const [bannersRes, activitiesRes, articlesRes] = await Promise.all([
        this.loadBanners(),
        this.loadActivities(),
        this.loadArticles()
      ])

      this.setData({
        banners: bannersRes,
        activities: activitiesRes,
        articles: articlesRes,
        isLoading: false
      })

      // 加载当前 Tab 的数据
      this.loadCurrentTabData()
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 加载轮播图数据
   */
  async loadBanners() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getBanners'
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data || []
      } else {
        throw new Error(res.result?.message || '获取轮播图失败')
      }
    } catch (error) {
      console.error('加载轮播图失败:', error)
      return []
    }
  },

  /**
   * 加载活动数据
   */
  async loadActivities() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getActivities'
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data.map(activity => ({
          ...activity,
          createTime: this.formatTime(activity.createTime)
        })).slice(0, 4) || [] // 只显示前4个活动
      } else {
        throw new Error(res.result?.message || '获取活动失败')
      }
    } catch (error) {
      console.error('加载活动失败:', error)
      return []
    }
  },

  /**
   * 加载文章数据
   */
  async loadArticles() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getArticles',
          limit: 3
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data.map(article => ({
          ...article,
          createTime: this.formatTime(article.createTime)
        })).slice(0, 3) || [] // 只显示前3篇文章
      } else {
        throw new Error(res.result?.message || '获取文章失败')
      }
    } catch (error) {
      console.error('加载文章失败:', error)
      return []
    }
  },

  /**
   * 初始化 Tab 指示器
   */
  initTabIndicator() {
    // 延迟执行，确保DOM已渲染
    setTimeout(() => {
      const query = wx.createSelectorQuery()
      query.selectAll('.tab-item').boundingClientRect()
      query.exec((res) => {
        if (res[0] && res[0].length > 0) {
          const firstTab = res[0][0]
          this.setData({
            indicatorLeft: firstTab.left,
            indicatorWidth: firstTab.width
          })
        }
      })
    }, 100)
  },

  /**
   * Tab 切换处理
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    if (tab === this.data.currentTab) return

    this.setData({ currentTab: tab })
    this.updateTabIndicator(tab)
    this.loadCurrentTabData()
  },

  /**
   * 更新 Tab 指示器位置
   */
  updateTabIndicator(currentTab) {
    const query = wx.createSelectorQuery()
    query.selectAll('.tab-item').boundingClientRect()
    query.exec((res) => {
      if (res[0] && res[0].length > 0) {
        const tabs = ['visit', 'activity', 'live']
        const currentIndex = tabs.indexOf(currentTab)

        if (currentIndex >= 0 && res[0][currentIndex]) {
          const targetTab = res[0][currentIndex]
          this.setData({
            indicatorLeft: targetTab.left,
            indicatorWidth: targetTab.width
          })
        }
      }
    })
  },

  /**
   * 加载当前 Tab 的数据
   */
  loadCurrentTabData() {
    const { currentTab } = this.data
    switch (currentTab) {
      case 'visit':
        this.loadVisitActivities()
        break
      case 'activity':
        this.loadCommunityActivities()
        break
      case 'live':
        this.loadLiveArticles()
        break
    }
  },

  /**
   * 导航到活动详情
   */
  navigateToActivityDetail(e) {
    const { id, type } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/user/detail/detail?id=${id}&type=activity&activityType=${type}`
    })
  },

  /**
   * 查看更多活动
   */
  viewMoreActivities() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 查看更多文章
   */
  viewMoreArticles() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 地区筛选变化 - 参观活动
   */
  onVisitRegionChange(e) {
    const region = e.detail.value
    let regionText = '全部地区'
    let cityValue = ''

    if (region.length > 0 && region[0] !== '全部地区') {
      // 根据选择的地区层级确定显示文本和筛选值
      if (region.length >= 2) {
        regionText = region[1] // 显示城市名
        cityValue = region[1] // 使用城市名作为筛选值
      } else if (region.length === 1) {
        regionText = region[0] // 显示省份名
        cityValue = region[0] // 使用省份名作为筛选值
      }
    }

    this.setData({
      selectedVisitRegion: region,
      visitRegionText: regionText,
      selectedVisitCity: cityValue
    })
    this.loadVisitActivities()
  },

  /**
   * 地区筛选变化 - 社区活动
   */
  onActivityRegionChange(e) {
    const region = e.detail.value
    let regionText = '全部地区'
    let cityValue = ''

    if (region.length > 0 && region[0] !== '全部地区') {
      // 根据选择的地区层级确定显示文本和筛选值
      if (region.length >= 2) {
        regionText = region[1] // 显示城市名
        cityValue = region[1] // 使用城市名作为筛选值
      } else if (region.length === 1) {
        regionText = region[0] // 显示省份名
        cityValue = region[0] // 使用省份名作为筛选值
      }
    }

    this.setData({
      selectedActivityRegion: region,
      activityRegionText: regionText,
      selectedActivityCity: cityValue
    })
    this.loadCommunityActivities()
  },

  /**
   * 加载社区参观活动数据
   */
  async loadVisitActivities() {
    if (this.data.visitLoading) return

    try {
      this.setData({ visitLoading: true })

      const requestData = {
        action: 'getActivities',
        type: 'visit'
      }

      // 添加城市筛选
      if (this.data.selectedVisitCity) {
        requestData.city = this.data.selectedVisitCity
      }

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: requestData
      })

      if (res.result && res.result.code === 0) {
        const visitActivities = res.result.data.map(activity => ({
          ...activity,
          createTime: this.formatTime(activity.createTime)
        }))

        this.setData({
          visitActivities,
          visitLoading: false
        })
      } else {
        throw new Error(res.result?.message || '获取参观活动失败')
      }
    } catch (error) {
      console.error('加载参观活动失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ visitLoading: false })
    }
  },

  /**
   * 加载社区活动数据
   */
  async loadCommunityActivities() {
    if (this.data.activityLoading) return

    try {
      this.setData({ activityLoading: true })

      const requestData = {
        action: 'getActivities',
        type: 'activity'
      }

      // 添加城市筛选
      if (this.data.selectedActivityCity) {
        requestData.city = this.data.selectedActivityCity
      }

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: requestData
      })

      if (res.result && res.result.code === 0) {
        const communityActivities = res.result.data.map(activity => ({
          ...activity,
          createTime: this.formatTime(activity.createTime)
        }))

        this.setData({
          communityActivities,
          activityLoading: false
        })
      } else {
        throw new Error(res.result?.message || '获取社区活动失败')
      }
    } catch (error) {
      console.error('加载社区活动失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ activityLoading: false })
    }
  },

  /**
   * 加载精彩现场文章数据
   */
  async loadLiveArticles() {
    if (this.data.liveLoading) return

    try {
      this.setData({ liveLoading: true })

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getArticles'
        }
      })

      if (res.result && res.result.code === 0) {
        const liveArticles = res.result.data.map(article => ({
          ...article,
          createTime: this.formatTime(article.createTime)
        }))

        this.setData({
          liveArticles,
          liveLoading: false
        })
      } else {
        throw new Error(res.result?.message || '获取精彩现场失败')
      }
    } catch (error) {
      console.error('加载精彩现场失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ liveLoading: false })
    }
  },

  /**
   * 导航到参观活动详情
   */
  navigateToVisitDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/user/detail/detail?id=${id}&type=activity&activityType=visit`
    })
  },

  /**
   * 导航到社区活动详情
   */
  navigateToCommunityActivityDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/user/detail/detail?id=${id}&type=activity&activityType=activity`
    })
  },

  /**
   * 导航到文章详情
   */
  navigateToArticle(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/user/article/article?id=${id}`
    })
  },

  /**
   * 导航到精彩现场文章详情
   */
  navigateToLiveArticle(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/user/article/article?id=${id}`
    })
  },

  /**
   * 拨打电话
   */
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: (error) => {
        console.error('拨打电话失败:', error)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 打开位置
   */
  openLocation() {
    wx.openLocation({
      latitude: 39.908823,
      longitude: 116.397470,
      name: '智慧养老康养中心',
      address: '北京市朝阳区康养街道123号',
      fail: (error) => {
        console.error('打开位置失败:', error)
        wx.showToast({
          title: '打开位置失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    if (!date) return ''

    const now = new Date()
    const target = new Date(date)
    const diff = now - target

    const minute = 60 * 1000
    const hour = minute * 60
    const day = hour * 24

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < day * 7) {
      return Math.floor(diff / day) + '天前'
    } else {
      return target.toLocaleDateString()
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 清空当前 Tab 数据，强制重新加载
    const { currentTab } = this.data
    switch (currentTab) {
      case 'visit':
        this.setData({ visitActivities: [] })
        break
      case 'activity':
        this.setData({ communityActivities: [] })
        break
      case 'live':
        this.setData({ liveArticles: [] })
        break
    }

    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智慧养老 - 专业的康养服务平台',
      path: '/pages/user/home/<USER>'
    }
  }
})