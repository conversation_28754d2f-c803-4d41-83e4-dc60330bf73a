/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 600rpx;
  text-align: center;
}

/* 头部 */
.header {
  margin-bottom: 80rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-icon {
  width: 300rpx;
  height: 300rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
}

/* 欢迎信息 */
.welcome {
  margin-bottom: 100rpx;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 16rpx;
}

.welcome-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 登录操作 */
.login-actions {
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: white;
  color: var(--primary-color);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 管理员登录按钮 */
.admin-login-btn {
  width: 100%;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.admin-login-btn:active {
  transform: translateY(2rpx);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.btn-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
}

.login-tips {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.login-tips .link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

/* 底部 */
.footer {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}