// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo, role } = event
  
  try {
    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get()
    
    let userData = null
    
    if (userQuery.data.length > 0) {
      // 用户已存在，更新信息
      userData = userQuery.data[0]
      
      // 更新用户信息
      await db.collection('users').doc(userData._id).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          role: role, // 允许切换角色
          updateTime: new Date()
        }
      })
      
      // 更新本地数据
      userData.nickName = userInfo.nickName
      userData.avatarUrl = userInfo.avatarUrl
      userData.role = role
      userData.updateTime = new Date()
      
    } else {
      // 新用户，创建记录
      const createResult = await db.collection('users').add({
        data: {
          openid: wxContext.OPENID,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          role: role,
          phone: '',
          createTime: new Date(),
          updateTime: new Date()
        }
      })
      
      // 获取创建的用户数据
      const newUserQuery = await db.collection('users').doc(createResult._id).get()
      userData = newUserQuery.data
    }
    
    return {
      code: 0,
      message: '登录成功',
      data: userData
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    return {
      code: -1,
      message: '登录失败，请重试',
      error: error.message
    }
  }
}
