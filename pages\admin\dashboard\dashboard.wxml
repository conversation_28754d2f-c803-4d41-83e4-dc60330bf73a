<!--pages/admin/dashboard/dashboard.wxml-->
<view class="dashboard-container">
  <!-- 欢迎区域 -->
  <view class="welcome-section">
    <view class="welcome-text">
      <view class="welcome-title">管理后台</view>
      <view class="welcome-subtitle">欢迎回来，{{adminInfo.nickName || '管理员'}}</view>
    </view>
    <view class="welcome-right">
      <view class="welcome-time">{{currentTime}}</view>
      <view class="logout-btn" bindtap="onNavigateBack">退出</view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <view class="stats-row">
      <view class="stats-card">
        <view class="stats-icon users">👥</view>
        <view class="stats-content">
          <view class="stats-number">{{stats.totalUsers}}</view>
          <view class="stats-label">总用户数</view>
        </view>
      </view>
      <view class="stats-card">
        <view class="stats-icon reservations">📅</view>
        <view class="stats-content">
          <view class="stats-number">{{stats.totalReservations}}</view>
          <view class="stats-label">总预约数</view>
        </view>
      </view>
    </view>
    <view class="stats-row">
      <view class="stats-card">
        <view class="stats-icon active">✅</view>
        <view class="stats-content">
          <view class="stats-number">{{stats.activeReservations}}</view>
          <view class="stats-label">进行中预约</view>
        </view>
      </view>
      <view class="stats-card">
        <view class="stats-icon activities">🎯</view>
        <view class="stats-content">
          <view class="stats-number">{{stats.totalActivities}}</view>
          <view class="stats-label">活动数量</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">
      <text class="title-icon">⚡</text>
      <text>快速操作</text>
    </view>
    <view class="actions-grid">
      <view class="action-item" bindtap="goToReservations">
        <view class="action-icon">📋</view>
        <view class="action-text">预约管理</view>
      </view>
      <view class="action-item" bindtap="goToContent">
        <view class="action-icon">📝</view>
        <view class="action-text">内容管理</view>
      </view>
    </view>
  </view>

  <!-- 最近预约 -->
  <view class="recent-section">
    <view class="section-title">
      <text class="title-icon">🕒</text>
      <text>最近预约</text>
      <text class="more-link" bindtap="goToReservations">查看全部</text>
    </view>
    <view class="recent-list">
      <view
        class="recent-item"
        wx:for="{{recentReservations}}"
        wx:key="id"
        bindtap="viewReservationDetail"
        data-id="{{item._id}}"
      >
        <view class="recent-info">
          <view class="recent-title">{{item.activityTitle || '康养服务'}}</view>
          <view class="recent-user">{{item.userName}} - {{item.userPhone}}</view>
          <view class="recent-time">{{item.reserveDate}}</view>
        </view>
        <view class="recent-status {{item.status}}">
          {{item.status === 'active' ? '进行中' : '已取消'}}
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{recentReservations.length === 0 && !isLoading}}">
      <view class="empty-icon">📅</view>
      <view class="empty-text">暂无最近预约</view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>