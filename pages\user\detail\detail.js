// pages/user/detail/detail.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    detail: {},
    images: [],
    id: '',
    type: 'activity', // activity 或 article
    showServiceModal: false // 客服弹窗显示状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id, type = 'activity' } = options
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ id, type })
    this.loadDetailData()
  },

  /**
   * 加载详情数据
   */
  async loadDetailData() {
    try {
      this.setData({ isLoading: true })

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: this.data.type === 'activity' ? 'getActivityDetail' : 'getArticleDetail',
          id: this.data.id
        }
      })

      if (res.result && res.result.code === 0) {
        const detail = res.result.data
        const images = []

        // 处理图片数组
        if (detail.imageUrl) {
          images.push(detail.imageUrl)
        }

        // 如果有其他图片，也添加到数组中
        if (detail.images && Array.isArray(detail.images)) {
          images.push(...detail.images)
        }

        this.setData({
          detail,
          images: images.length > 0 ? images : ['/images/default-image.jpg'],
          isLoading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: detail.title || '详情'
        })
      } else {
        throw new Error(res.result?.message || '加载失败')
      }
    } catch (error) {
      console.error('加载详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const current = e.currentTarget.dataset.current
    wx.previewImage({
      current,
      urls: this.data.images
    })
  },

  /**
   * 弹出提示
   */
  makePhoneCall() {
    wx.showModal({
      title: '提示',
      content: '请前往公众号留言/致电了解',
      showCancel: false, // 不显示取消按钮
      confirmText: '知道了',
      confirmColor: '#2196F3' // 与导航栏颜色保持一致
    });
  },

  /**
   * 打开位置
   */
  openLocation() {
    const location = this.data.detail.location
    if (!location) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      })
      return
    }

    wx.openLocation({
      latitude: 39.908823, // 默认坐标，实际应该从数据库获取
      longitude: 116.397470,
      name: this.data.detail.title,
      address: location,
      fail: (error) => {
        console.error('打开位置失败:', error)
        wx.showToast({
          title: '打开位置失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到预约页面
   */
  navigateToBooking() {
    // 检查登录状态，未登录则跳转到登录页面
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.openid) {
      wx.navigateTo({
        url: '/pages/login/login'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/user/booking/booking?activityId=${this.data.id}&type=${this.data.detail.type || 'activity'}`
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadDetailData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 显示客服二维码
   */
  showCustomerService() {
    this.setData({
      showServiceModal: true
    })
  },

  /**
   * 隐藏客服二维码
   */
  hideCustomerService() {
    this.setData({
      showServiceModal: false
    })
  },



  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.detail.title || '康养服务详情',
      path: `/pages/user/detail/detail?id=${this.data.id}&type=${this.data.type}`
    }
  }
})