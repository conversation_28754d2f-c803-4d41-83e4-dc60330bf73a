<!--pages/user/home/<USER>
<view class="home-container">
  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{banners.length > 1}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#ffffff" autoplay="{{banners.length > 1}}" interval="4000" duration="500" circular="{{banners.length > 1}}">
      <swiper-item wx:for="{{banners}}" wx:key="id" class="banner-item">
        <image src="{{item.imageUrl}}" class="banner-image" mode="aspectFill" lazy-load="true" />
        <view class="banner-overlay">
          <view class="banner-title">{{item.title}}</view>
        </view>
      </swiper-item>
    </swiper>
  </view>
  <!-- 顶部Tab（同一行包含社区参观、社区活动、精彩现场） -->
  <view class="tab-container">
    <!-- 社区参观 Tab -->
    <view class="tab-item {{currentTab === 'visit' ? 'active' : ''}}" bindtap="switchTab" data-tab="visit">
      <text class="tab-text">社区参观</text>
    </view>

    <!-- 社区活动 Tab -->
    <view class="tab-item {{currentTab === 'activity' ? 'active' : ''}}" bindtap="switchTab" data-tab="activity">
      <text class="tab-text">社区活动</text>
    </view>

    <!-- 精彩现场 Tab -->
    <view class="tab-item {{currentTab === 'live' ? 'active' : ''}}" bindtap="switchTab" data-tab="live">
      <text class="tab-text">精彩现场</text>
    </view>

    <!-- 底部选中指示器 -->
    <view class="tab-indicator" style="left: {{indicatorLeft}}px; width: {{indicatorWidth}}px;"></view>
  </view>
  <!-- Tab 内容区域 -->
  <view class="tab-content">
    <!-- 社区参观内容 -->
    <view class="tab-panel" wx:if="{{currentTab === 'visit'}}">
      <!-- 地区筛选 -->
      <view class="filter-section">
        <view class="filter-label">地区筛选：</view>
        <picker class="region-picker" mode="region" bindchange="onVisitRegionChange" value="{{selectedVisitRegion}}" custom-item="全部地区">
          <view class="picker-text">{{visitRegionText}}  ▼</view>
        </picker>
      </view>

      <!-- 加载状态 -->
      <view class="loading-mini" wx:if="{{visitLoading}}">
        <text>加载中...</text>
      </view>

      <!-- 参观活动列表 -->
      <view class="activities-list" wx:if="{{!visitLoading}}">
        <view class="activity-item" wx:for="{{visitActivities}}" wx:key="id" bindtap="navigateToVisitDetail" data-id="{{item._id}}">
          <image src="{{item.imageUrl}}" class="activity-image" mode="aspectFill" />
          <view class="activity-content">
            <view class="activity-header">
              <view class="activity-title">{{item.title}}</view>
            </view>
            <view class="activity-desc" style="width: 310rpx; display: -webkit-box; box-sizing: border-box">{{item.description}}</view>
            <view class="activity-meta">
              <view class="activity-location" wx:if="{{item.location}}">
                <text class="meta-icon">📍</text>
                <text>{{item.location}}</text>
              </view>
            </view>
          </view>
          <view class="activity-action">
            <view class="action-btn">查看详情</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!visitLoading && visitActivities.length === 0}}">
        <view class="empty-icon">🏛️</view>
        <view class="empty-text">暂无参观活动</view>
      </view>
    </view>

    <!-- 社区活动内容 -->
    <view class="tab-panel" wx:if="{{currentTab === 'activity'}}">
      <!-- 地区筛选 -->
      <view class="filter-section">
        <view class="filter-label">地区筛选：</view>
        <picker class="region-picker" mode="region" bindchange="onActivityRegionChange" value="{{selectedActivityRegion}}" custom-item="全部地区">
          <view class="picker-text">{{activityRegionText}}  ▼</view>
        </picker>
      </view>

      <!-- 加载状态 -->
      <view class="loading-mini" wx:if="{{activityLoading}}">
        <text>加载中...</text>
      </view>

      <!-- 社区活动列表 -->
      <view class="activities-list" wx:if="{{!activityLoading}}">
        <view class="activity-item" wx:for="{{communityActivities}}" wx:key="id" bindtap="navigateToCommunityActivityDetail" data-id="{{item._id}}">
          <image src="{{item.imageUrl}}" class="activity-image" mode="aspectFill" />
          <view class="activity-content">
            <view class="activity-header">
              <view class="activity-title">{{item.title}}</view>
              <view class="activity-type activity">活动</view>
            </view>
            <view class="activity-desc">{{item.description}}</view>
            <view class="activity-meta">
              <view class="activity-location" wx:if="{{item.location}}">
                <text class="meta-icon">📍</text>
                <text>{{item.location}}</text>
              </view>
              <view class="activity-time">{{item.createTime}}</view>
            </view>
          </view>
          <view class="activity-action">
            <view class="action-btn">查看详情</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!activityLoading && communityActivities.length === 0}}">
        <view class="empty-icon">🎯</view>
        <view class="empty-text">暂无社区活动</view>
      </view>
    </view>

    <!-- 精彩现场内容 -->
    <view class="tab-panel" wx:if="{{currentTab === 'live'}}">

      <!-- 加载状态 -->
      <view class="loading-mini" wx:if="{{liveLoading}}">
        <text>加载中...</text>
      </view>

      <!-- 精彩现场文章列表 -->
      <view class="articles-list" wx:if="{{!liveLoading}}">
        <view class="article-item" wx:for="{{liveArticles}}" wx:key="id" bindtap="navigateToLiveArticle" data-id="{{item._id}}">
          <image src="{{item.imageUrl}}" class="article-image" mode="aspectFill" />
          <view class="article-content">
            <view class="article-title">{{item.title}}</view>
            <view class="article-summary">{{item.summary}}</view>
            <view class="article-time">{{item.createTime}}</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!liveLoading && liveArticles.length === 0}}">
        <view class="empty-icon">📰</view>
        <view class="empty-text">暂无精彩现场</view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{isLoading}}">
  <text>加载中...</text>
</view>