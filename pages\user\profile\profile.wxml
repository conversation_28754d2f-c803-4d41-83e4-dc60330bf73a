<!--pages/user/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-avatar">
      <image src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-image" />
    </view>
    <view class="user-info">
      <view class="user-name">{{userInfo.nickName || '未设置昵称'}}</view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="stats-item" bindtap="goToReserved">
      <view class="stats-number">{{stats.total}}</view>
      <view class="stats-label">总预约</view>
    </view>
    <view class="stats-item" bindtap="goToReserved" data-filter="active">
      <view class="stats-number">{{stats.active}}</view>
      <view class="stats-label">进行中</view>
    </view>
    <view class="stats-item">
      <view class="stats-number">{{stats.completed}}</view>
      <view class="stats-label">已完成</view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="editProfile">
        <view class="menu-icon">👤</view>
        <view class="menu-text">我的信息</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="goToReserved">
        <view class="menu-icon">📅</view>
        <view class="menu-text">我的预约</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="showAbout">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <view class="logout-btn" bindtap="logout">
      <text>退出登录</text>
    </view>
  </view>

  <!-- 编辑信息弹窗 -->
  <view class="modal-overlay" wx:if="{{showEditModal}}" bindtap="closeEditModal">
    <view class="edit-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">编辑个人信息</text>
        <view class="modal-close" bindtap="closeEditModal">×</view>
      </view>
      
      <view class="modal-content">
        <!-- 头像上传 -->
        <view class="form-item">
          <view class="form-label">头像</view>
          <view class="avatar-upload" bindtap="chooseAvatar">
            <image src="{{editForm.avatarUrl || userInfo.avatarUrl || '/images/default-avatar.png'}}" class="upload-avatar" />
            <view class="upload-text">点击更换头像</view>
          </view>
        </view>

        <!-- 昵称输入 -->
        <view class="form-item">
          <view class="form-label">昵称</view>
          <input class="form-input" 
                 placeholder="请输入昵称" 
                 value="{{editForm.nickName}}" 
                 data-field="nickName"
                 bindinput="onEditInputChange" />
        </view>

        <!-- 地址输入 -->
        <view class="form-item">
          <view class="form-label">地址</view>
          <textarea class="form-textarea" 
                    placeholder="请输入详细地址" 
                    value="{{editForm.address}}" 
                    data-field="address"
                    bindinput="onEditInputChange" />
        </view>
      </view>

      <view class="modal-actions">
        <view class="modal-btn cancel" bindtap="closeEditModal">取消</view>
        <view class="modal-btn confirm" bindtap="saveProfile">保存</view>
      </view>
    </view>
  </view>
</view>