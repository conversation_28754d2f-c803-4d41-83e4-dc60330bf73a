// pages/user/reserved/reserved.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    currentFilter: 'all', // all, active, cancelled
    reservations: [],
    allReservations: [], // 所有预约数据
    filteredReservations: [],
    stats: {
      total: 0,
      active: 0,
      completed: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadReservations()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.loadReservations()
  },

  /**
   * 加载预约数据
   */
  async loadReservations() {
    try {
      this.setData({ isLoading: true })

      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.openid) {
        wx.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'getUserReservations',
          userId: userInfo.openid
        }
      })

      if (res.result && res.result.code === 0) {
        const reservations = res.result.data.map(item => ({
          ...item,
          reserveDate: item.reserveDate
        }))

        // 计算统计数据
        const stats = {
          total: reservations.length,
          active: reservations.filter(item => item.status === 'active').length,
          completed: reservations.filter(item => item.status === 'cancelled').length
        }

        this.setData({
          reservations,
          allReservations: reservations, // 同时设置allReservations
          stats,
          isLoading: false
        })

        this.filterReservations()
      } else {
        throw new Error(res.result?.message || '加载失败')
      }
    } catch (error) {
      console.error('加载预约数据失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 切换筛选条件
   */
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterReservations()
  },

  /**
   * 筛选预约数据
   */
  filterReservations() {
    const { reservations, currentFilter } = this.data
    let filteredReservations = []

    switch (currentFilter) {
      case 'all':
        filteredReservations = reservations
        break
      case 'active':
        filteredReservations = reservations.filter(item => item.status === 'active')
        break
      case 'cancelled':
        filteredReservations = reservations.filter(item => item.status === 'cancelled')
        break
    }

    this.setData({ filteredReservations })
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const phone = e.currentTarget.dataset.phone
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: (error) => {
        console.error('拨打电话失败:', error)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 取消预约
   */
  cancelReservation(e) {
    const id = e.currentTarget.dataset.id

    // 显示取消原因输入框
    wx.showModal({
      title: '取消预约',
      content: '',
      editable: true,
      placeholderText: '请输入取消原因',
      confirmText: '确认取消',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          const cancelReason = res.content.trim()
          if (!cancelReason) {
            wx.showToast({
              title: '请输入取消原因',
              icon: 'none'
            })
            return
          }
          this.performCancelReservation(id, cancelReason)
        }
      }
    })
  },

  /**
   * 执行取消预约
   */
  async performCancelReservation(id, cancelReason) {
    try {
      wx.showLoading({
        title: '取消中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'cancelReservation',
          reservationId: id,
          cancelReason: cancelReason
        }
      })

      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadReservations()
      } else {
        throw new Error(res.result.message || '取消失败')
      }
    } catch (error) {
      console.error('取消预约失败:', error)
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 编辑用户备注
   */
  editUserNote(e) {
    const { id, note } = e.currentTarget.dataset

    wx.showModal({
      title: '编辑备注',
      editable: true,
      placeholderText: '请输入备注内容',
      content: note || '',
      confirmText: '保存',
      success: (res) => {
        if (res.confirm) {
          const newNote = res.content.trim()
          this.saveUserNote(id, newNote)
        }
      }
    })
  },

  /**
   * 保存用户备注
   */
  async saveUserNote(reservationId, note) {
    try {
      wx.showLoading({
        title: '保存中...'
      })

      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.openid) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        return
      }

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'updateUserNote',
          reservationId: reservationId,
          userId: userInfo.openid,
          userNote: note
        }
      })

      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 更新本地数据
        const allReservations = this.data.allReservations || []
        const reservations = allReservations.map(item => {
          if (item._id === reservationId) {
            return { ...item, userNote: note }
          }
          return item
        })

        this.setData({ allReservations: reservations })
        this.filterReservations()
      } else {
        throw new Error(res.result?.message || '保存失败')
      }
    } catch (error) {
      console.error('保存备注失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 去首页预约
   */
  goToHome() {
    wx.switchTab({
      url: '/pages/user/home/<USER>'
    })
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return ''

    const date = new Date(dateStr)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const diffDays = Math.floor((targetDate - today) / (24 * 60 * 60 * 1000))

    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '明天'
    } else if (diffDays === -1) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReservations().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})