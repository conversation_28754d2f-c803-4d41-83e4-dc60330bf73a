<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- 主要内容 -->
  <view class="login-content">
    <!-- Logo和标题 -->
    <view class="header">
      <view class="logo">
        <image src="cloud://cloud1-7g5mgqcd5fe2118d.636c-cloud1-7g5mgqcd5fe2118d-1370083019/logo.png" class="logo-icon"></image>
      </view>
      <view class="title">智慧养老</view>
      <view class="subtitle">专业的康养服务平台</view>
      <view class="subtitle">养老社区免费一键预约</view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-actions">
      <button class="login-btn" open-type="getUserInfo" bindgetuserinfo="handleLogin" loading="{{isLoading}}">
        <text class="btn-text">微信一键登录</text>
      </button>

      <view style="font-size: 26rpx;color: white;">
        <text style="text-decoration: underline; cursor: pointer;" bindtap="navigateToHome">
          暂不登录？点此返回首页
        </text>
      </view>

      <!-- 管理员登录按钮 -->
      <!-- <button
        class="admin-login-btn"
        open-type="getUserInfo"
        bindgetuserinfo="handleAdminLogin"
        loading="{{isLoading}}"
      >
        <text class="btn-text">管理员登录</text>
      </button> -->

    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="footer-text">让关爱更贴心，让生活更美好</view>
  </view>
</view>