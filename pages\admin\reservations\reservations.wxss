/* pages/admin/reservations/reservations.wxss */
.reservations-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 顶部统计 */
.stats-header {
  background: var(--surface-color);
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-number {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.stats-number.active {
  color: var(--primary-color);
}

.stats-number.cancelled {
  color: #F44336;
}

.stats-number.today {
  color: #FF9800;
}

.stats-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 筛选和搜索区域 */
.filter-section {
  background: var(--surface-color);
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  background: var(--background-color);
  text-align: center;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: var(--primary-color);
  color: #2196F3;
}

.search-box {
  display: flex;
  align-items: center;
  background: var(--background-color);
  border-radius: 12rpx;
  padding: 0 20rpx;
  border: 2rpx solid var(--divider-color);
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.search-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 预约列表 */
.reservations-list {
  padding: 0 20rpx;
}

.reservation-item {
  background: var(--surface-color);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
}

.status-indicator.active {
  background: var(--primary-color);
}

.status-indicator.cancelled {
  background: #F44336;
}

/* 预约内容 */
.reservation-content {
  padding: 30rpx 30rpx 30rpx 38rpx;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.user-phone {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
}

/* 服务信息 */
.service-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.service-title {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.type-tag {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.type-tag.visit {
  background: #4CAF50;
}

.type-tag.activity {
  background: #FF9800;
}

/* 时间信息 */
.time-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.time-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.time-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 备注信息 */
.remark-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.remark-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  margin-top: 2rpx;
}

.remark-text {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 取消原因 */
.cancel-reason {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.cancel-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  margin-top: 2rpx;
}

.cancel-text {
  flex: 1;
  font-size: 26rpx;
  color: #F44336;
  line-height: 1.4;
}

/* 创建时间 */
.create-time {
  font-size: 22rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 预约操作 */
.reservation-actions {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-badge.active {
  background: #E8F5E8;
  color: #4CAF50;
}

.status-badge.cancelled {
  background: #FFEBEE;
  color: #F44336;
}

.action-buttons {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  text-align: center;
  min-width: 60rpx;
}

.action-btn.call {
  background: #E3F2FD;
  color: var(--primary-color);
}

.action-btn.cancel {
  background: #FFEBEE;
  color: #F44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}