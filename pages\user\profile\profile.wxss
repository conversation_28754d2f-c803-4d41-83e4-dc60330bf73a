/* pages/user/profile/profile.wxss */
.profile-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  padding: 60rpx 30rpx 40rpx;
  display: flex;
  align-items: center;
  color: white;
  margin-bottom: 20rpx;
}

.user-avatar {
  margin-right: 24rpx;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color:rgba(0, 0, 0);
}

.user-phone {
  font-size: 26rpx;
  opacity: 0.8;
  color:rgba(0, 0, 0);
}

.edit-btn {
  padding: 12rpx 24rpx;
  background: rgba(33, 150, 243);
  border-radius: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 统计数据区域 */
.stats-section {
  background: var(--surface-color);
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 菜单区域 */
.menu-section {
  margin-bottom: 40rpx;
}

.menu-group {
  background: var(--surface-color);
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--divider-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary);
}

.menu-arrow {
  font-size: 32rpx;
  color: var(--text-secondary);
}

/* 退出登录区域 */
.logout-section {
  padding: 0 30rpx;
}

.logout-btn {
  background: var(--surface-color);
  color: #ff4444;
  text-align: center;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: 1rpx solid #ffcdd2;
}

.logout-btn:active {
  background: #ffebee;
}

/* 编辑弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--divider-color);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  font-size: 40rpx;
  color: var(--text-secondary);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: var(--background-color);
  border: 2rpx solid var(--divider-color);
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
}

/* 头像上传样式 */
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: var(--background-color);
  border: 2rpx dashed var(--divider-color);
  border-radius: 12rpx;
}

.avatar-upload:active {
  border-color: var(--primary-color);
  background: rgba(33, 150, 243, 0.05);
}

.upload-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid var(--divider-color);
}

.upload-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 地址输入框样式 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: var(--background-color);
  border: 2rpx solid var(--divider-color);
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid var(--divider-color);
}

.modal-btn {
  flex: 1;
  text-align: center;
  padding: 30rpx;
  font-size: 30rpx;
}

.modal-btn.cancel {
  color: var(--text-secondary);
  border-right: 1rpx solid var(--divider-color);
}

.modal-btn.confirm {
  color: var(--primary-color);
  font-weight: 500;
}