// 表单验证工具类

/**
 * 验证规则类型
 */
const RuleTypes = {
  REQUIRED: 'required',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  PHONE: 'phone',
  EMAIL: 'email',
  NUMBER: 'number',
  INTEGER: 'integer',
  POSITIVE: 'positive',
  RANGE: 'range',
  PATTERN: 'pattern',
  CUSTOM: 'custom'
};

/**
 * 默认错误消息
 */
const DefaultMessages = {
  [RuleTypes.REQUIRED]: '此字段为必填项',
  [RuleTypes.MIN_LENGTH]: '长度不能少于{min}个字符',
  [RuleTypes.MAX_LENGTH]: '长度不能超过{max}个字符',
  [RuleTypes.PHONE]: '请输入正确的手机号',
  [RuleTypes.EMAIL]: '请输入正确的邮箱地址',
  [RuleTypes.NUMBER]: '请输入有效的数字',
  [RuleTypes.INTEGER]: '请输入整数',
  [RuleTypes.POSITIVE]: '请输入正数',
  [RuleTypes.RANGE]: '数值应在{min}到{max}之间',
  [RuleTypes.PATTERN]: '格式不正确'
};

/**
 * 验证器类
 */
class Validator {
  constructor() {
    this.rules = {};
    this.messages = {};
  }

  /**
   * 添加验证规则
   * @param {string} field 字段名
   * @param {Array} rules 规则数组
   * @param {Object} messages 自定义错误消息
   */
  addRule(field, rules, messages = {}) {
    this.rules[field] = rules;
    this.messages[field] = messages;
    return this;
  }

  /**
   * 验证单个字段
   * @param {string} field 字段名
   * @param {any} value 字段值
   * @returns {Object} 验证结果
   */
  validateField(field, value) {
    const rules = this.rules[field];
    if (!rules || rules.length === 0) {
      return { valid: true };
    }

    for (let rule of rules) {
      const result = this.applyRule(field, value, rule);
      if (!result.valid) {
        return result;
      }
    }

    return { valid: true };
  }

  /**
   * 验证所有字段
   * @param {Object} data 数据对象
   * @returns {Object} 验证结果
   */
  validate(data) {
    const errors = {};
    let isValid = true;

    for (let field in this.rules) {
      const value = data[field];
      const result = this.validateField(field, value);
      
      if (!result.valid) {
        errors[field] = result.message;
        isValid = false;
      }
    }

    return {
      valid: isValid,
      errors: errors,
      firstError: isValid ? null : {
        field: Object.keys(errors)[0],
        message: Object.values(errors)[0]
      }
    };
  }

  /**
   * 应用单个规则
   * @param {string} field 字段名
   * @param {any} value 字段值
   * @param {Object} rule 规则对象
   * @returns {Object} 验证结果
   */
  applyRule(field, value, rule) {
    const { type, params = {}, message } = rule;
    const customMessage = message || this.messages[field]?.[type];
    
    switch (type) {
      case RuleTypes.REQUIRED:
        return this.validateRequired(value, customMessage);
      
      case RuleTypes.MIN_LENGTH:
        return this.validateMinLength(value, params.min, customMessage);
      
      case RuleTypes.MAX_LENGTH:
        return this.validateMaxLength(value, params.max, customMessage);
      
      case RuleTypes.PHONE:
        return this.validatePhone(value, customMessage);
      
      case RuleTypes.EMAIL:
        return this.validateEmail(value, customMessage);
      
      case RuleTypes.NUMBER:
        return this.validateNumber(value, customMessage);
      
      case RuleTypes.INTEGER:
        return this.validateInteger(value, customMessage);
      
      case RuleTypes.POSITIVE:
        return this.validatePositive(value, customMessage);
      
      case RuleTypes.RANGE:
        return this.validateRange(value, params.min, params.max, customMessage);
      
      case RuleTypes.PATTERN:
        return this.validatePattern(value, params.pattern, customMessage);
      
      case RuleTypes.CUSTOM:
        return this.validateCustom(value, params.validator, customMessage);
      
      default:
        return { valid: true };
    }
  }

  /**
   * 必填验证
   */
  validateRequired(value, message) {
    const isEmpty = value === null || value === undefined || 
                   (typeof value === 'string' && value.trim() === '') ||
                   (Array.isArray(value) && value.length === 0);
    
    return {
      valid: !isEmpty,
      message: message || DefaultMessages[RuleTypes.REQUIRED]
    };
  }

  /**
   * 最小长度验证
   */
  validateMinLength(value, min, message) {
    if (value === null || value === undefined) {
      return { valid: true };
    }
    
    const length = String(value).length;
    const isValid = length >= min;
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.MIN_LENGTH].replace('{min}', min)
    };
  }

  /**
   * 最大长度验证
   */
  validateMaxLength(value, max, message) {
    if (value === null || value === undefined) {
      return { valid: true };
    }
    
    const length = String(value).length;
    const isValid = length <= max;
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.MAX_LENGTH].replace('{max}', max)
    };
  }

  /**
   * 手机号验证
   */
  validatePhone(value, message) {
    if (!value) {
      return { valid: true };
    }
    
    const phoneRegex = /^1[3-9]\d{9}$/;
    const isValid = phoneRegex.test(value);
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.PHONE]
    };
  }

  /**
   * 邮箱验证
   */
  validateEmail(value, message) {
    if (!value) {
      return { valid: true };
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.EMAIL]
    };
  }

  /**
   * 数字验证
   */
  validateNumber(value, message) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    
    const isValid = !isNaN(Number(value)) && isFinite(Number(value));
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.NUMBER]
    };
  }

  /**
   * 整数验证
   */
  validateInteger(value, message) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    
    const num = Number(value);
    const isValid = Number.isInteger(num);
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.INTEGER]
    };
  }

  /**
   * 正数验证
   */
  validatePositive(value, message) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    
    const num = Number(value);
    const isValid = !isNaN(num) && num > 0;
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.POSITIVE]
    };
  }

  /**
   * 范围验证
   */
  validateRange(value, min, max, message) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    
    const num = Number(value);
    const isValid = !isNaN(num) && num >= min && num <= max;
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.RANGE]
        .replace('{min}', min)
        .replace('{max}', max)
    };
  }

  /**
   * 正则表达式验证
   */
  validatePattern(value, pattern, message) {
    if (!value) {
      return { valid: true };
    }
    
    const regex = new RegExp(pattern);
    const isValid = regex.test(value);
    
    return {
      valid: isValid,
      message: message || DefaultMessages[RuleTypes.PATTERN]
    };
  }

  /**
   * 自定义验证
   */
  validateCustom(value, validator, message) {
    try {
      const result = validator(value);
      
      if (typeof result === 'boolean') {
        return {
          valid: result,
          message: message || '验证失败'
        };
      }
      
      return result;
    } catch (error) {
      return {
        valid: false,
        message: message || '验证出错'
      };
    }
  }
}

/**
 * 创建验证器实例
 * @returns {Validator} 验证器实例
 */
const createValidator = () => {
  return new Validator();
};

/**
 * 快速验证函数
 * @param {Object} data 数据对象
 * @param {Object} rules 规则对象
 * @returns {Object} 验证结果
 */
const validate = (data, rules) => {
  const validator = createValidator();
  
  for (let field in rules) {
    validator.addRule(field, rules[field]);
  }
  
  return validator.validate(data);
};

/**
 * 常用验证规则
 */
const CommonRules = {
  required: { type: RuleTypes.REQUIRED },
  phone: { type: RuleTypes.PHONE },
  email: { type: RuleTypes.EMAIL },
  number: { type: RuleTypes.NUMBER },
  integer: { type: RuleTypes.INTEGER },
  positive: { type: RuleTypes.POSITIVE },
  
  minLength: (min) => ({ type: RuleTypes.MIN_LENGTH, params: { min } }),
  maxLength: (max) => ({ type: RuleTypes.MAX_LENGTH, params: { max } }),
  range: (min, max) => ({ type: RuleTypes.RANGE, params: { min, max } }),
  pattern: (pattern) => ({ type: RuleTypes.PATTERN, params: { pattern } }),
  custom: (validator) => ({ type: RuleTypes.CUSTOM, params: { validator } })
};

module.exports = {
  RuleTypes,
  DefaultMessages,
  Validator,
  createValidator,
  validate,
  CommonRules
};
