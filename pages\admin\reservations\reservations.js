// pages/admin/reservations/reservations.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    currentFilter: 'all',
    searchKeyword: '',
    reservations: [],
    filteredReservations: [],
    stats: {
      total: 0,
      active: 0,
      cancelled: 0,
      today: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkAdminAuth()
    this.loadReservations()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadReservations()
  },

  /**
   * 检查管理员权限
   */
  checkAdminAuth() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限，无法访问此页面',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
      return false
    }
    return true
  },

  /**
   * 加载预约数据
   */
  async loadReservations() {
    try {
      this.setData({ isLoading: true })

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'getReservationsForAdmin'
        }
      })

      if (res.result && res.result.code === 0) {
        const reservations = res.result.data.map(item => ({
          ...item,
          reserveDate: item.reserveDate,
          createTime: item.createTime
        }))

        // 计算统计数据
        const stats = this.calculateStats(reservations)

        this.setData({
          reservations,
          stats,
          isLoading: false
        })

        this.filterReservations()
      } else {
        throw new Error(res.result.message || '加载失败')
      }
    } catch (error) {
      console.error('加载预约数据失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 计算统计数据
   */
  calculateStats(reservations) {
    const today = new Date()
    const todayStr = today.toDateString()

    return {
      total: reservations.length,
      active: reservations.filter(item => item.status === 'active').length,
      cancelled: reservations.filter(item => item.status === 'cancelled').length,
      today: reservations.filter(item => {
        const reserveDate = new Date(item.reserveTime)
        return reserveDate.toDateString() === todayStr
      }).length
    }
  },

  /**
   * 切换筛选条件
   */
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      currentFilter: filter,
      searchKeyword: ''
    })
    this.filterReservations()
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })
  },

  /**
   * 执行搜索
   */
  performSearch() {
    this.filterReservations()
  },

  /**
   * 筛选预约数据
   */
  filterReservations() {
    const { reservations, currentFilter, searchKeyword } = this.data
    let filteredReservations = [...reservations]

    // 按状态筛选
    switch (currentFilter) {
      case 'active':
        filteredReservations = filteredReservations.filter(item => item.status === 'active')
        break
      case 'cancelled':
        filteredReservations = filteredReservations.filter(item => item.status === 'cancelled')
        break
      case 'today':
        const todayStr = new Date().toDateString()
        filteredReservations = filteredReservations.filter(item => {
          const reserveDate = new Date(item.reserveTime)
          return reserveDate.toDateString() === todayStr
        })
        break
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase()
      filteredReservations = filteredReservations.filter(item =>
        item.userName.toLowerCase().includes(keyword) ||
        item.userPhone.includes(keyword)
      )
    }

    this.setData({ filteredReservations })
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const phone = e.currentTarget.dataset.phone
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: (error) => {
        console.error('拨打电话失败:', error)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 取消预约
   */
  cancelReservation(e) {
    const id = e.currentTarget.dataset.id

    // 显示取消原因输入框
    wx.showModal({
      title: '取消预约',
      content: '',
      editable: true,
      placeholderText: '请输入取消原因',
      confirmText: '确认取消',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          const cancelReason = res.content.trim()
          if (!cancelReason) {
            wx.showToast({
              title: '请输入取消原因',
              icon: 'none'
            })
            return
          }
          this.performCancelReservation(id, cancelReason)
        }
      }
    })
  },

  /**
   * 执行取消预约
   */
  async performCancelReservation(id, cancelReason) {
    try {
      wx.showLoading({
        title: '取消中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'reservation',
        data: {
          action: 'cancelReservation',
          reservationId: id,
          cancelReason: cancelReason
        }
      })

      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadReservations()
      } else {
        throw new Error(res.result.message || '取消失败')
      }
    } catch (error) {
      console.error('取消预约失败:', error)
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTime) {
    if (!dateTime) return ''

    const date = new Date(dateTime)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const diffDays = Math.floor((targetDate - today) / (24 * 60 * 60 * 1000))

    const timeStr = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })

    if (diffDays === 0) {
      return `今天 ${timeStr}`
    } else if (diffDays === 1) {
      return `明天 ${timeStr}`
    } else if (diffDays === -1) {
      return `昨天 ${timeStr}`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      }) + ` ${timeStr}`
    }
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return ''

    const date = new Date(dateStr)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const diffDays = Math.floor((targetDate - today) / (24 * 60 * 60 * 1000))

    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '明天'
    } else if (diffDays === -1) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReservations().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})