// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  
  try {
    switch (action) {
      case 'createReservation':
        return await createReservation(event)
      case 'getUserReservations':
        return await getUserReservations(event)
      case 'cancelReservation':
        return await cancelReservation(event)
      case 'updateUserNote':
        return await updateUserNote(event)
      case 'deleteReservation':
        return await deleteReservation(event)
      case 'getReservationsForAdmin':
        return await getReservationsForAdmin(event)
      case 'getReservationStats':
        return await getReservationStats(event)
      case 'getRecentReservations':
        return await getRecentReservations(event)
      case 'getReservationDetail':
        return await getReservationDetail(event)
      default:
        return {
          code: -1,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('预约操作失败:', error)
    return {
      code: -1,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 创建预约
async function createReservation(event) {
  const { userId, activityId, userName, userPhone, reserveDate, remark } = event

  // 验证数据
  if (!userId || !activityId || !userName || !userPhone || !reserveDate) {
    return {
      code: -1,
      message: '参数不完整'
    }
  }
  
  // 验证手机号
  if (!/^1[3-9]\d{9}$/.test(userPhone)) {
    return {
      code: -1,
      message: '手机号格式不正确'
    }
  }
  
  // 验证预约日期（必须是未来日期）
  const reserveDate_obj = new Date(reserveDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  if (reserveDate_obj < today) {
    return {
      code: -1,
      message: '预约日期必须是今天或未来日期'
    }
  }
  
  // 检查活动是否存在且可预约
  const activityResult = await db.collection('activities').doc(activityId).get()
  if (!activityResult.data || activityResult.data.status !== 'published') {
    return {
      code: -1,
      message: '活动不存在或已下线'
    }
  }
  
  // 检查用户是否已预约同一活动
  const existingReservation = await db.collection('reservations')
    .where({
      userId: userId,
      activityId: activityId,
      status: 'active'
    })
    .get()
  
  if (existingReservation.data.length > 0) {
    return {
      code: -1,
      message: '您已预约过该活动'
    }
  }
  
  // 创建预约记录
  const result = await db.collection('reservations').add({
    data: {
      userId: userId,
      activityId: activityId,
      userName: userName.trim(),
      userPhone: userPhone.trim(),
      reserveDate: reserveDate,
      remark: remark ? remark.trim() : '',
      status: 'active',
      createTime: new Date(),
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '预约成功',
    data: { _id: result._id }
  }
}

// 获取用户预约列表
async function getUserReservations(event) {
  const { userId } = event

  // 验证参数
  if (!userId) {
    return {
      code: -1,
      message: '用户ID不能为空'
    }
  }

  const result = await db.collection('reservations')
    .where({
      userId: userId
    })
    .orderBy('createTime', 'desc')
    .get()
  
  // 获取关联的活动信息
  const reservationsWithActivity = await Promise.all(
    result.data.map(async (reservation) => {
      const activityResult = await db.collection('activities').doc(reservation.activityId).get()
      const activity = activityResult.data || {}
      return {
        ...reservation,
        activity: activity,
        activityTitle: activity.title || '康养服务',
        activityType: activity.type || 'visit'
      }
    })
  )
  
  return {
    code: 0,
    message: '获取成功',
    data: reservationsWithActivity
  }
}

// 取消预约
async function cancelReservation(event) {
  const { reservationId, cancelReason } = event
  
  // 获取预约信息
  const reservationResult = await db.collection('reservations').doc(reservationId).get()
  if (!reservationResult.data) {
    return {
      code: -1,
      message: '预约记录不存在'
    }
  }
  
  const reservation = reservationResult.data
  
  // 检查预约状态
  if (reservation.status !== 'active') {
    return {
      code: -1,
      message: '预约已取消或无效'
    }
  }
  
  // 检查是否可以取消（预约日期前一天）
  const reserveDate_obj = new Date(reservation.reserveDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  if (reserveDate_obj <= today) {
    return {
      code: -1,
      message: '预约日期当天或之后无法取消预约'
    }
  }
  
  // 更新预约状态
  await db.collection('reservations').doc(reservationId).update({
    data: {
      status: 'cancelled',
      cancelReason: cancelReason || '',
      cancelTime: new Date(),
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '取消成功'
  }
}

// 删除预约记录
async function deleteReservation(event) {
  const { reservationId } = event

  await db.collection('reservations').doc(reservationId).remove()

  return {
    code: 0,
    message: '删除成功'
  }
}

// 更新用户备注
async function updateUserNote(event) {
  const { reservationId, userId, userNote } = event

  // 验证参数
  if (!reservationId || !userId) {
    return {
      code: -1,
      message: '参数不完整'
    }
  }

  // 验证预约记录是否存在且属于该用户
  const reservation = await db.collection('reservations').doc(reservationId).get()

  if (!reservation.data) {
    return {
      code: -1,
      message: '预约记录不存在'
    }
  }

  if (reservation.data.userId !== userId) {
    return {
      code: -1,
      message: '无权限操作'
    }
  }

  // 更新用户备注
  await db.collection('reservations').doc(reservationId).update({
    data: {
      userNote: userNote || '',
      updateTime: new Date()
    }
  })

  return {
    code: 0,
    message: '备注保存成功'
  }
}

// 获取预约列表（管理端）
async function getReservationsForAdmin(event) {
  const { status, keyword, page = 1, pageSize = 10 } = event
  
  let query = db.collection('reservations')
  
  // 状态筛选
  if (status) {
    query = query.where({
      status: status
    })
  }
  
  // 关键词搜索
  if (keyword) {
    query = query.where(_.or([
      {
        userName: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      },
      {
        userPhone: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      }
    ]))
  }
  
  const result = await query
    .orderBy('createTime', 'desc')
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .get()
  
  // 获取关联的活动信息
  const reservationsWithActivity = await Promise.all(
    result.data.map(async (reservation) => {
      const activityResult = await db.collection('activities').doc(reservation.activityId).get()
      const activity = activityResult.data || {}
      return {
        ...reservation,
        activity: activity,
        activityTitle: activity.title || '康养服务',
        activityType: activity.type || 'visit'
      }
    })
  )
  
  return {
    code: 0,
    message: '获取成功',
    data: reservationsWithActivity
  }
}

// 获取预约统计数据
async function getReservationStats(event) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  const [totalResult, activeResult, todayResult, cancelledResult] = await Promise.all([
    db.collection('reservations').count(),
    db.collection('reservations').where({ status: 'active' }).count(),
    db.collection('reservations').where({
      createTime: _.gte(today).and(_.lt(tomorrow))
    }).count(),
    db.collection('reservations').where({ status: 'cancelled' }).count()
  ])
  
  return {
    code: 0,
    message: '获取成功',
    data: {
      total: totalResult.total,
      active: activeResult.total,
      today: todayResult.total,
      cancelled: cancelledResult.total
    }
  }
}

// 获取最近预约
async function getRecentReservations(event) {
  const { limit = 5 } = event

  try {
    const result = await db.collection('reservations')
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get()

    // 获取关联的活动信息
    const reservationsWithActivity = await Promise.all(
      result.data.map(async (reservation) => {
        try {
          const activityResult = await db.collection('activities').doc(reservation.activityId).get()
          const activity = activityResult.data || {}
          return {
            ...reservation,
            activity: activity,
            activityTitle: activity.title || '康养服务',
            activityType: activity.type || 'visit'
          }
        } catch (error) {
          console.error('获取活动信息失败:', error)
          return {
            ...reservation,
            activity: {},
            activityTitle: '康养服务',
            activityType: 'visit'
          }
        }
      })
    )

    return {
      success: true,
      code: 0,
      message: '获取成功',
      data: reservationsWithActivity
    }
  } catch (error) {
    console.error('获取最近预约失败:', error)
    return {
      success: false,
      code: -1,
      message: '获取最近预约失败',
      error: error.message
    }
  }
}

// 获取预约详情
async function getReservationDetail(event) {
  const { reservationId } = event
  
  const reservationResult = await db.collection('reservations').doc(reservationId).get()
  if (!reservationResult.data) {
    return {
      code: -1,
      message: '预约记录不存在'
    }
  }
  
  const reservation = reservationResult.data
  
  // 获取关联的活动信息
  const activityResult = await db.collection('activities').doc(reservation.activityId).get()
  
  return {
    code: 0,
    message: '获取成功',
    data: {
      ...reservation,
      activity: activityResult.data || {}
    }
  }
}
