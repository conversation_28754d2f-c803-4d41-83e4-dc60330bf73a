// pages/admin/content/content.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    currentTab: 'activities',
    searchKeyword: '',
    activities: [],
    articles: [],
    banners: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkAdminAuth()
    this.loadAllContent()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadAllContent()
  },

  /**
   * 检查管理员权限
   */
  checkAdminAuth() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限，无法访问此页面',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
      return false
    }
    return true
  },

  /**
   * 加载所有内容
   */
  async loadAllContent() {
    try {
      this.setData({ isLoading: true })

      const [activitiesRes, articlesRes, bannersRes] = await Promise.all([
        this.loadActivities(),
        this.loadArticles(),
        this.loadBanners()
      ])

      this.setData({
        activities: activitiesRes,
        articles: articlesRes,
        banners: bannersRes,
        isLoading: false
      })
    } catch (error) {
      console.error('加载内容失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 加载活动列表
   */
  async loadActivities() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getActivitiesForAdmin'
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data.map(item => ({
          ...item,
          createTime: this.formatDate(item.createTime)
        }))
      } else {
        throw new Error(res.result?.message || '获取活动列表失败')
      }
    } catch (error) {
      console.error('加载活动失败:', error)
      return []
    }
  },

  /**
   * 加载文章列表
   */
  async loadArticles() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getArticlesForAdmin'
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data.map(item => ({
          ...item,
          createTime: this.formatDate(item.createTime)
        }))
      } else {
        throw new Error(res.result?.message || '获取文章列表失败')
      }
    } catch (error) {
      console.error('加载文章失败:', error)
      return []
    }
  },

  /**
   * 加载轮播图列表
   */
  async loadBanners() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: 'getBannersForAdmin'
        }
      })

      if (res.result && res.result.code === 0) {
        return res.result.data.map(item => ({
          ...item,
          createTime: this.formatDate(item.createTime)
        }))
      } else {
        throw new Error(res.result?.message || '获取轮播图列表失败')
      }
    } catch (error) {
      console.error('加载轮播图失败:', error)
      return []
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab,
      searchKeyword: ''
    })
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })
  },

  /**
   * 执行搜索
   */
  performSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    })
  },

  /**
   * 添加内容
   */
  addContent() {
    const { currentTab } = this.data
    let contentType = 'activity'

    switch (currentTab) {
      case 'activities':
        contentType = 'activity'
        break
      case 'articles':
        contentType = 'article'
        break
      case 'banners':
        contentType = 'banner'
        break
    }

    wx.navigateTo({
      url: `/pages/admin/content/edit/edit?type=${contentType}`
    })
  },

  /**
   * 编辑内容
   */
  editContent(e) {
    const { id, type } = e.currentTarget.dataset

    if (!id) {
      wx.showToast({
        title: '内容ID不存在',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/admin/content/edit/edit?type=${type}&id=${id}`
    })
  },

  /**
   * 删除内容
   */
  deleteContent(e) {
    const { id, type } = e.currentTarget.dataset

    wx.showModal({
      title: '确认删除',
      content: `确定要删除这个${type === 'activity' ? '活动' : type === 'article' ? '文章' : '轮播图'}吗？删除后无法恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(id, type)
        }
      }
    })
  },

  /**
   * 执行删除
   */
  async performDelete(id, type) {
    try {
      wx.showLoading({
        title: '删除中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action: `delete${type.charAt(0).toUpperCase() + type.slice(1)}`,
          id: id
        }
      })

      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadAllContent()
      } else {
        throw new Error(res.result?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 获取当前列表
   */
  getCurrentList() {
    const { currentTab } = this.data
    switch (currentTab) {
      case 'activities':
        return this.data.activities
      case 'articles':
        return this.data.articles
      case 'banners':
        return this.data.banners
      default:
        return []
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    if (!date) return ''

    const d = new Date(date)
    return d.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAllContent().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})