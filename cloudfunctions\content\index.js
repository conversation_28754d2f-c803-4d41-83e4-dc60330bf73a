// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  
  try {
    switch (action) {
      // 轮播图相关
      case 'getBanners':
        return await getBanners(event)
      case 'getBannersForAdmin':
        return await getBannersForAdmin(event)
      case 'getBannerDetail':
        return await getBannerDetail(event)
      case 'createBanner':
        return await createBanner(event)
      case 'updateBanner':
        return await updateBanner(event)
      case 'updateBannerStatus':
        return await updateBannerStatus(event)
      case 'deleteBanner':
        return await deleteBanner(event)
      
      // 活动相关
      case 'getActivities':
        return await getActivities(event)
      case 'getActivitiesForAdmin':
        return await getActivitiesForAdmin(event)
      case 'getActivityDetail':
        return await getActivityDetail(event)
      case 'createActivity':
        return await createActivity(event)
      case 'updateActivity':
        return await updateActivity(event)
      case 'updateActivityStatus':
        return await updateActivityStatus(event)
      case 'deleteActivity':
        return await deleteActivity(event)
      
      // 文章相关
      case 'getArticles':
        return await getArticles(event)
      case 'getArticlesForAdmin':
        return await getArticlesForAdmin(event)
      case 'getArticleDetail':
        return await getArticleDetail(event)
      case 'createArticle':
        return await createArticle(event)
      case 'updateArticle':
        return await updateArticle(event)
      case 'updateArticleStatus':
        return await updateArticleStatus(event)
      case 'deleteArticle':
        return await deleteArticle(event)
      
      // 统计相关
      case 'getDashboardStats':
        return await getDashboardStats(event)
      
      default:
        return {
          code: -1,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('内容操作失败:', error)
    return {
      code: -1,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 获取轮播图（用户端）
async function getBanners(event) {
  const result = await db.collection('banners')
    .where({
      status: 'active'
    })
    .orderBy('sort', 'asc')
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取轮播图（管理端）
async function getBannersForAdmin(event) {
  const { keyword } = event
  
  let query = db.collection('banners')
  
  if (keyword) {
    query = query.where({
      title: db.RegExp({
        regexp: keyword,
        options: 'i'
      })
    })
  }
  
  const result = await query
    .orderBy('sort', 'asc')
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 创建轮播图
async function createBanner(event) {
  const { data } = event
  
  const result = await db.collection('banners').add({
    data: {
      ...data,
      status: 'active',
      createTime: new Date(),
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '创建成功',
    data: { _id: result._id }
  }
}

// 更新轮播图
async function updateBanner(event) {
  const { data } = event
  const { _id, ...updateData } = data
  
  await db.collection('banners').doc(_id).update({
    data: {
      ...updateData,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '更新成功'
  }
}

// 更新轮播图状态
async function updateBannerStatus(event) {
  const { id, status } = event
  
  await db.collection('banners').doc(id).update({
    data: {
      status: status,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '状态更新成功'
  }
}

// 删除轮播图
async function deleteBanner(event) {
  const { id } = event

  await db.collection('banners').doc(id).remove()

  return {
    code: 0,
    message: '删除成功'
  }
}

// 获取轮播图详情
async function getBannerDetail(event) {
  const { id } = event

  const result = await db.collection('banners').doc(id).get()

  if (!result.data) {
    return {
      code: -1,
      message: '轮播图不存在'
    }
  }

  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取活动列表（用户端）
async function getActivities(event) {
  const { type, city } = event

  let whereCondition = {
    status: 'published'
  }

  // 如果指定了类型，则按类型筛选
  if (type) {
    whereCondition.type = type
  }

  // 如果指定了城市，则按城市筛选
  if (city) {
    whereCondition.city = city
  }

  const result = await db.collection('activities')
    .where(whereCondition)
    .orderBy('createTime', 'desc')
    .get()

  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取活动列表（管理端）
async function getActivitiesForAdmin(event) {
  const { keyword } = event
  
  let query = db.collection('activities')
  
  if (keyword) {
    query = query.where({
      title: db.RegExp({
        regexp: keyword,
        options: 'i'
      })
    })
  }
  
  const result = await query
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取活动详情
async function getActivityDetail(event) {
  const { id } = event
  
  const result = await db.collection('activities').doc(id).get()
  
  if (!result.data) {
    return {
      code: -1,
      message: '活动不存在'
    }
  }
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 创建活动
async function createActivity(event) {
  const { data } = event
  
  const result = await db.collection('activities').add({
    data: {
      ...data,
      status: 'draft',
      createTime: new Date(),
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '创建成功',
    data: { _id: result._id }
  }
}

// 更新活动
async function updateActivity(event) {
  const { data } = event
  const { _id, ...updateData } = data
  
  await db.collection('activities').doc(_id).update({
    data: {
      ...updateData,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '更新成功'
  }
}

// 更新活动状态
async function updateActivityStatus(event) {
  const { id, status } = event
  
  await db.collection('activities').doc(id).update({
    data: {
      status: status,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '状态更新成功'
  }
}

// 删除活动
async function deleteActivity(event) {
  const { id } = event
  
  // 检查是否有相关预约
  const reservationCheck = await db.collection('reservations')
    .where({
      activityId: id,
      status: 'active'
    })
    .count()
  
  if (reservationCheck.total > 0) {
    return {
      code: -1,
      message: '该活动还有有效预约，无法删除'
    }
  }
  
  await db.collection('activities').doc(id).remove()
  
  return {
    code: 0,
    message: '删除成功'
  }
}

// 获取文章列表（用户端）
async function getArticles(event) {
  const result = await db.collection('articles')
    .where({
      status: 'published'
    })
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取文章列表（管理端）
async function getArticlesForAdmin(event) {
  const { keyword } = event
  
  let query = db.collection('articles')
  
  if (keyword) {
    query = query.where({
      title: db.RegExp({
        regexp: keyword,
        options: 'i'
      })
    })
  }
  
  const result = await query
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    code: 0,
    message: '获取成功',
    data: result.data
  }
}

// 获取文章详情
async function getArticleDetail(event) {
  const { id } = event

  const result = await db.collection('articles').doc(id).get()

  if (!result.data) {
    return {
      code: -1,
      message: '文章不存在'
    }
  }

  // 直接返回文章数据，不处理图片链接
  const article = result.data

  return {
    code: 0,
    message: '获取成功',
    data: article
  }
}



// 创建文章
async function createArticle(event) {
  const { data } = event
  
  const result = await db.collection('articles').add({
    data: {
      ...data,
      status: 'draft',
      createTime: new Date(),
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '创建成功',
    data: { _id: result._id }
  }
}

// 更新文章
async function updateArticle(event) {
  const { data } = event
  const { _id, ...updateData } = data
  
  await db.collection('articles').doc(_id).update({
    data: {
      ...updateData,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '更新成功'
  }
}

// 更新文章状态
async function updateArticleStatus(event) {
  const { id, status } = event
  
  await db.collection('articles').doc(id).update({
    data: {
      status: status,
      updateTime: new Date()
    }
  })
  
  return {
    code: 0,
    message: '状态更新成功'
  }
}

// 删除文章
async function deleteArticle(event) {
  const { id } = event
  
  await db.collection('articles').doc(id).remove()
  
  return {
    code: 0,
    message: '删除成功'
  }
}

// 获取仪表板统计数据
async function getDashboardStats(event) {
  const [usersResult, reservationsResult, activitiesResult, articlesResult] = await Promise.all([
    db.collection('users').count(),
    db.collection('reservations').count(),
    db.collection('activities').count(),
    db.collection('articles').count()
  ])
  
  return {
    code: 0,
    message: '获取成功',
    data: {
      totalUsers: usersResult.total,
      totalReservations: reservationsResult.total,
      totalActivities: activitiesResult.total,
      totalArticles: articlesResult.total
    }
  }
}
