/* pages/user/detail/detail.wxss */
.detail-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 图片区域 */
.image-section {
  margin-bottom: 20rpx;
}

.detail-swiper {
  height: 500rpx;
}

.swiper-item {
  height: 100%;
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 基本信息区域 */
.info-section {
  background: var(--surface-color);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.title-area {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.main-title {
  flex: 1;
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  margin-right: 20rpx;
}

.type-tag {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

.description {
  font-size: 30rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 详细介绍区域 */
.intro-section {
  margin-bottom: 20rpx;
}

.intro-item {
  background: var(--surface-color);
  padding: 30rpx;
  margin-bottom: 16rpx;
}

.intro-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.intro-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.intro-content {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 通用区域标题 */
.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  padding: 30rpx 30rpx 20rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

/* 位置信息区域 */
.location-section {
  height: 100rpx;
  background: var(--surface-color);
  display: flex;
  align-items: center;
  border-top: 1rpx solid #e0e0e0;
}

.location-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx 30rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.location-arrow {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-left: 16rpx;
}

/* 联系方式区域 */
.contact-section {
  background: var(--surface-color);
  height: 100rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #e0e0e0;
  border-bottom: 1rpx solid #e0e0e0;
}

.contact-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx 30rpx;
}

.contact-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.contact-arrow {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-left: 16rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(30rpx);
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #2196F3;
  color: white;
  border: 2rpx solid var(--divider-color);
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.action-btn.primary {
  background: #2196F3;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon-1 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 20rpx;
}

/* 客服二维码弹窗 */
.customer-service-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--divider-color);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-secondary);
  border-radius: 50%;
  background: var(--background-color);
}

.modal-close:active {
  background: var(--divider-color);
}

.qr-code-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qr-code-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  border: 1rpx solid var(--divider-color);
  margin-bottom: 30rpx;
}

.qr-code-tip {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}