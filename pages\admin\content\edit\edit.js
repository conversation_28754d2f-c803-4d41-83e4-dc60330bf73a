// pages/admin/content/edit/edit.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: false,
    isSubmitting: false,
    isEdit: false,
    contentType: 'activity', // activity, article, banner
    contentId: '',
    typeText: '活动',
    formData: {
      title: '',
      imageUrl: '',
      images: [], // 多图片数组
      status: 'draft',

      // 活动特有字段
      type: 'visit', // visit, activity
      description: '',
      location: '',
      phone: '',
      viewCount: 0,
      city: '',

      // 文章特有字段
      summary: '',
      content: '',

      // 轮播图特有字段
      linkUrl: '',
      sort: '1'
    },

    // 富文本编辑器相关
    editorReady: false,
    editorContext: null,
    editorContent: '',
    wordCount: 0,
    autoSaveTimer: null,

    // 编辑器工具栏状态
    formats: {},

    // 图片上传状态
    imageUploading: false,

    // 地区选择相关
    selectedRegion: [],
    regionText: '请选择地区'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkAdminAuth()

    const { type, id } = options

    // 设置内容类型
    if (type) {
      this.setContentType(type)
    }

    // 如果有ID，则是编辑模式
    if (id) {
      this.setData({
        isEdit: true,
        contentId: id
      })
      this.loadContentDetail(id)
    }

    // 初始化自动保存
    this.initAutoSave()
  },

  /**
   * 检查管理员权限
   */
  checkAdminAuth() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限，无法访问此页面',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
      return false
    }
    return true
  },

  /**
   * 设置内容类型
   */
  setContentType(type) {
    let typeText = '活动'
    let defaultStatus = 'draft'
    
    switch (type) {
      case 'activity':
        typeText = '活动'
        break
      case 'article':
        typeText = '文章'
        break
      case 'banner':
        typeText = '轮播图'
        defaultStatus = 'active'
        break
    }
    
    this.setData({
      contentType: type,
      typeText,
      'formData.status': defaultStatus
    })
  },

  /**
   * 加载内容详情
   */
  async loadContentDetail(id) {
    try {
      this.setData({ isLoading: true })
      
      const { contentType } = this.data
      const action = `get${contentType.charAt(0).toUpperCase() + contentType.slice(1)}Detail`
      
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action,
          id
        }
      })
      
      if (res.result.code === 0) {
        const data = res.result.data

        // 处理地区数据
        let selectedRegion = []
        let regionText = '请选择地区'

        if (data.city) {
          // 这里简化处理，实际应该根据城市名反推省份
          // 由于微信地区选择器的限制，我们只能显示城市名
          regionText = data.city
          // 注意：这里无法完美还原省市区的选择状态，只能显示城市名
        }

        // 更新表单数据
        this.setData({
          formData: data,
          selectedRegion: selectedRegion,
          regionText: regionText,
          isLoading: false
        })

        // 如果是文章且编辑器已就绪，设置编辑器内容
        if (this.data.contentType === 'article' && this.data.editorReady && data.content) {
          this.setEditorContent(data.content)
        }
      } else {
        throw new Error(res.result.message || '获取内容详情失败')
      }
    } catch (error) {
      console.error('加载内容详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 表单输入处理函数
   */
  onTitleInput(e) {
    this.setData({ 'formData.title': e.detail.value })
  },
  
  onTypeChange(e) {
    this.setData({ 'formData.type': e.currentTarget.dataset.type })
  },
  
  onDescriptionInput(e) {
    this.setData({ 'formData.description': e.detail.value })
  },
  

  
  onLocationInput(e) {
    this.setData({ 'formData.location': e.detail.value })
  },
  
  onPhoneInput(e) {
    this.setData({ 'formData.phone': e.detail.value })
  },

  onViewCountInput(e) {
    this.setData({ 'formData.viewCount': parseInt(e.detail.value) || 0 })
  },

  onRegionChange(e) {
    const region = e.detail.value
    let regionText = '请选择地区'
    let cityValue = ''

    if (region.length > 0) {
      // 根据选择的地区层级确定显示文本和存储值
      if (region.length >= 2) {
        regionText = `${region[0]} ${region[1]}` // 显示省份+城市
        cityValue = region[1] // 存储城市名
      } else if (region.length === 1) {
        regionText = region[0] // 显示省份名
        cityValue = region[0] // 存储省份名
      }
    }

    this.setData({
      selectedRegion: region,
      regionText: regionText,
      'formData.city': cityValue
    })
  },
  
  onSummaryInput(e) {
    this.setData({ 'formData.summary': e.detail.value })
  },
  
  onContentInput(e) {
    this.setData({ 'formData.content': e.detail.value })
  },

  /**
   * 富文本编辑器相关方法
   */

  // 编辑器初始化完成
  onEditorReady() {
    const that = this
    wx.createSelectorQuery().select('#editor').context(function (res) {
      that.setData({
        editorContext: res.context,
        editorReady: true
      })

      // 如果是编辑模式且有内容，设置编辑器内容
      if (that.data.isEdit && that.data.formData.content) {
        that.setEditorContent(that.data.formData.content)
      } else if (!that.data.isEdit) {
        // 新建模式下检查是否有草稿
        that.loadDraft()
      }
    }).exec()
  },

  // 编辑器内容变化
  onEditorInput(e) {
    const { html, text } = e.detail
    this.setData({
      editorContent: html,
      wordCount: text.length,
      'formData.content': html
    })

    // 触发自动保存
    this.triggerAutoSave()
  },

  // 编辑器获得焦点
  onEditorFocus(e) {
    console.log('编辑器获得焦点')
  },

  // 编辑器失去焦点
  onEditorBlur(e) {
    console.log('编辑器失去焦点')
  },

  // 编辑器状态变化
  onStatusChange(e) {
    this.setData({
      formats: e.detail
    })
  },

  // 设置编辑器内容
  setEditorContent(html) {
    if (this.data.editorContext) {
      this.data.editorContext.setContents({
        html: html
      })
    }
  },

  // 格式化文本
  formatText(e) {
    const { type } = e.currentTarget.dataset
    if (!this.data.editorContext) return

    switch (type) {
      case 'bold':
        this.data.editorContext.format('bold')
        break
      case 'italic':
        this.data.editorContext.format('italic')
        break
      case 'underline':
        this.data.editorContext.format('underline')
        break
      case 'strike':
        this.data.editorContext.format('strike')
        break
      case 'header':
        const level = e.currentTarget.dataset.level || '1'
        this.data.editorContext.format('header', level)
        break
      case 'align':
        const align = e.currentTarget.dataset.align || 'left'
        this.data.editorContext.format('align', align)
        break
      case 'list':
        const listType = e.currentTarget.dataset.listType || 'ordered'
        this.data.editorContext.format('list', listType)
        break
      case 'indent':
        const direction = e.currentTarget.dataset.direction || '+1'
        this.data.editorContext.format('indent', direction)
        break
    }
  },

  // 设置字体大小
  setFontSize(e) {
    const size = e.currentTarget.dataset.size
    if (this.data.editorContext) {
      this.data.editorContext.format('size', size)
    }
  },

  // 设置文字颜色
  setTextColor(e) {
    const color = e.currentTarget.dataset.color
    if (this.data.editorContext) {
      this.data.editorContext.format('color', color)
    }
  },

  // 插入图片到编辑器
  async insertImageToEditor() {
    if (!this.data.editorContext) {
      wx.showToast({
        title: '编辑器未就绪',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ imageUploading: true })

      const res = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed']
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        const tempFilePath = res.tempFiles[0].tempFilePath

        wx.showLoading({
          title: '上传中...',
          mask: true
        })

        // 上传图片到云存储
        const uploadRes = await this.uploadFileToEditor(tempFilePath)

        // 插入图片到编辑器
        this.data.editorContext.insertImage({
          src: uploadRes.fileID,
          alt: '图片',
          width: '100%',
          height: 'auto'
        })

        wx.hideLoading()
        wx.showToast({
          title: '图片插入成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('插入图片失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '插入图片失败',
        icon: 'none'
      })
    } finally {
      this.setData({ imageUploading: false })
    }
  },

  // 上传文件到编辑器专用目录
  uploadFileToEditor(filePath) {
    return new Promise((resolve, reject) => {
      const cloudPath = `articles/images/${Date.now()}-${Math.floor(Math.random() * 1000)}${filePath.match(/\.[^.]+?$/)[0]}`

      wx.cloud.uploadFile({
        cloudPath,
        filePath,
        success: res => {
          resolve({
            fileID: res.fileID
          })
        },
        fail: err => {
          reject(err)
        }
      })
    })
  },

  // 插入链接
  insertLink() {
    if (!this.data.editorContext) return

    wx.showModal({
      title: '插入链接',
      editable: true,
      placeholderText: '请输入链接地址',
      success: (res) => {
        if (res.confirm && res.content) {
          this.data.editorContext.format('link', res.content)
        }
      }
    })
  },

  // 插入表格
  insertTable() {
    if (!this.data.editorContext) return

    // 插入一个3x3的基础表格
    const tableHtml = `
      <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格1</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格2</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格3</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格4</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格5</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格6</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格7</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格8</td>
          <td style="padding: 8px; border: 1px solid #ddd;">单元格9</td>
        </tr>
      </table>
    `

    this.data.editorContext.insertText({
      text: ' '
    })
    this.data.editorContext.insertDivider()
    this.data.editorContext.insertText({
      text: tableHtml
    })
  },

  // 撤销
  undo() {
    if (this.data.editorContext) {
      this.data.editorContext.undo()
    }
  },

  // 重做
  redo() {
    if (this.data.editorContext) {
      this.data.editorContext.redo()
    }
  },

  // 初始化自动保存
  initAutoSave() {
    // 每30秒自动保存草稿
    this.data.autoSaveTimer = setInterval(() => {
      this.autoSaveDraft()
    }, 30000)
  },

  // 触发自动保存
  triggerAutoSave() {
    // 清除之前的定时器
    if (this.data.autoSaveTimer) {
      clearTimeout(this.data.autoSaveTimer)
    }

    // 3秒后自动保存
    this.data.autoSaveTimer = setTimeout(() => {
      this.autoSaveDraft()
    }, 3000)
  },

  // 自动保存草稿
  autoSaveDraft() {
    if (this.data.contentType !== 'article' || !this.data.formData.title.trim()) {
      return
    }

    const draftKey = `draft_${this.data.contentType}_${this.data.contentId || 'new'}`
    const draftData = {
      ...this.data.formData,
      content: this.data.editorContent,
      saveTime: new Date().toISOString()
    }

    try {
      wx.setStorageSync(draftKey, draftData)
      console.log('草稿已自动保存')
    } catch (error) {
      console.error('自动保存失败:', error)
    }
  },

  // 加载草稿
  loadDraft() {
    const draftKey = `draft_${this.data.contentType}_${this.data.contentId || 'new'}`

    try {
      const draftData = wx.getStorageSync(draftKey)
      if (draftData && draftData.saveTime) {
        wx.showModal({
          title: '发现草稿',
          content: `发现${new Date(draftData.saveTime).toLocaleString()}保存的草稿，是否恢复？`,
          success: (res) => {
            if (res.confirm) {
              this.setData({
                formData: draftData
              })

              // 设置编辑器内容
              if (this.data.editorReady && draftData.content) {
                this.setEditorContent(draftData.content)
              }
            }
          }
        })
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  },

  // 清除草稿
  clearDraft() {
    const draftKey = `draft_${this.data.contentType}_${this.data.contentId || 'new'}`
    try {
      wx.removeStorageSync(draftKey)
    } catch (error) {
      console.error('清除草稿失败:', error)
    }
  },
  
  onLinkUrlInput(e) {
    this.setData({ 'formData.linkUrl': e.detail.value })
  },
  
  onSortInput(e) {
    this.setData({ 'formData.sort': e.detail.value })
  },
  
  onStatusChange(e) {
    this.setData({ 'formData.status': e.currentTarget.dataset.status })
  },

  /**
   * 图片处理函数
   */
  async chooseImage() {
    try {
      const currentImages = this.data.formData.images || []
      const maxCount = 8 - currentImages.length

      if (maxCount <= 0) {
        wx.showToast({
          title: '最多只能上传8张图片',
          icon: 'none'
        })
        return
      }

      const res = await wx.chooseMedia({
        count: maxCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed']
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        wx.showLoading({
          title: '上传中...',
          mask: true
        })

        // 批量上传图片
        const uploadPromises = res.tempFiles.map(file => this.uploadFile(file.tempFilePath))
        const uploadResults = await Promise.all(uploadPromises)

        // 更新图片数组
        const newImages = [...currentImages, ...uploadResults.map(result => result.fileID)]

        // 如果没有主图，将第一张图片设为主图
        const updateData = {
          'formData.images': newImages
        }

        if (!this.data.formData.imageUrl && newImages.length > 0) {
          updateData['formData.imageUrl'] = newImages[0]
        }

        this.setData(updateData)
        wx.hideLoading()
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  },
  
  /**
   * 上传文件到云存储
   */
  uploadFile(filePath) {
    return new Promise((resolve, reject) => {
      const cloudPath = `content/${this.data.contentType}/${Date.now()}-${Math.floor(Math.random() * 1000)}${filePath.match(/\.[^.]+?$/)[0]}`
      
      wx.cloud.uploadFile({
        cloudPath,
        filePath,
        success: res => {
          resolve({
            fileID: res.fileID
          })
        },
        fail: err => {
          reject(err)
        }
      })
    })
  },
  
  /**
   * 预览图片
   */
  previewImage() {
    wx.previewImage({
      urls: [this.data.formData.imageUrl]
    })
  },
  
  /**
   * 删除主图
   */
  removeImage() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除主图吗？',
      success: (res) => {
        if (res.confirm) {
          const images = this.data.formData.images || []
          // 如果有其他图片，将第二张图片设为主图
          const newImageUrl = images.length > 1 ? images[1] : ''

          this.setData({
            'formData.imageUrl': newImageUrl
          })
        }
      }
    })
  },

  /**
   * 删除多图片中的单张
   */
  removeImageFromList(e) {
    const index = e.currentTarget.dataset.index
    const imageUrl = e.currentTarget.dataset.url

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          const images = [...this.data.formData.images]
          images.splice(index, 1)

          const updateData = {
            'formData.images': images
          }

          // 如果删除的是主图，需要更新主图
          if (this.data.formData.imageUrl === imageUrl) {
            updateData['formData.imageUrl'] = images.length > 0 ? images[0] : ''
          }

          this.setData(updateData)
        }
      }
    })
  },

  /**
   * 设置为主图
   */
  setAsMainImage(e) {
    const imageUrl = e.currentTarget.dataset.url
    this.setData({
      'formData.imageUrl': imageUrl
    })
    wx.showToast({
      title: '已设为主图',
      icon: 'success'
    })
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const current = e.currentTarget.dataset.url || this.data.formData.imageUrl
    const urls = this.data.formData.images.length > 0 ? this.data.formData.images : [this.data.formData.imageUrl]

    wx.previewImage({
      current: current,
      urls: urls.filter(url => url) // 过滤空值
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, contentType } = this.data
    
    // 通用验证
    if (!formData.title.trim()) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return false
    }
    
    if (!formData.imageUrl) {
      wx.showToast({
        title: '请上传主图',
        icon: 'none'
      })
      return false
    }
    
    // 活动特有验证
    if (contentType === 'activity') {
      if (!formData.description.trim()) {
        wx.showToast({
          title: '请输入活动描述',
          icon: 'none'
        })
        return false
      }
    }
    
    // 文章特有验证
    if (contentType === 'article') {
      if (!formData.summary.trim()) {
        wx.showToast({
          title: '请输入文章摘要',
          icon: 'none'
        })
        return false
      }
      
      if (!formData.content.trim()) {
        wx.showToast({
          title: '请输入文章内容',
          icon: 'none'
        })
        return false
      }
    }
    
    // 轮播图特有验证
    if (contentType === 'banner') {
      if (!formData.sort) {
        wx.showToast({
          title: '请输入排序数字',
          icon: 'none'
        })
        return false
      }
    }
    
    return true
  },

  /**
   * 提交表单
   */
  async handleSubmit() {
    // 表单验证
    if (!this.validateForm()) {
      return
    }
    
    try {
      this.setData({ isSubmitting: true })
      
      const { contentType, isEdit, contentId, formData } = this.data
      
      // 准备云函数参数
      const action = isEdit 
        ? `update${contentType.charAt(0).toUpperCase() + contentType.slice(1)}`
        : `create${contentType.charAt(0).toUpperCase() + contentType.slice(1)}`
      
      const data = { ...formData }
      
      // 如果是编辑模式，添加ID
      if (isEdit) {
        data._id = contentId
      }
      
      // 调用云函数
      const res = await wx.cloud.callFunction({
        name: 'content',
        data: {
          action,
          data
        }
      })
      
      if (res.result.code === 0) {
        // 清除草稿
        this.clearDraft()

        wx.showToast({
          title: isEdit ? '更新成功' : '创建成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(res.result.message || (isEdit ? '更新失败' : '创建失败'))
      }
    } catch (error) {
      console.error('提交表单失败:', error)
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isSubmitting: false })
    }
  },

  /**
   * 重置表单
   */
  handleReset() {
    if (this.data.isEdit) {
      // 编辑模式下重新加载数据
      this.loadContentDetail(this.data.contentId)
    } else {
      // 新建模式下重置表单
      this.setData({
        formData: {
          title: '',
          imageUrl: '',
          images: [],
          status: this.data.contentType === 'banner' ? 'active' : 'draft',
          type: 'visit',
          description: '',
          location: '',
          phone: '',
          viewCount: 0,
          city: '',
          summary: '',
          content: '',
          linkUrl: '',
          sort: '1'
        },
        selectedRegion: [],
        regionText: '请选择地区'
      })
    }
  },

  /**
   * 取消操作
   */
  handleCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消编辑吗？未保存的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  /**
   * 页面标题设置
   */
  onReady() {
    const { isEdit, typeText } = this.data
    wx.setNavigationBarTitle({
      title: `${isEdit ? '编辑' : '添加'}${typeText}`
    })
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    // 清除自动保存定时器
    if (this.data.autoSaveTimer) {
      clearInterval(this.data.autoSaveTimer)
      clearTimeout(this.data.autoSaveTimer)
    }
  }
})
